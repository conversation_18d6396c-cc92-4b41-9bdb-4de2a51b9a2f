
import os
import sys
from typing import List
import pandas as pd

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    from utils.logger_config import setup_logger
    import ai_office.office_tools as tools
    import ai_office.from_oletool as ole
    import ai_office.from_yara as yara
    import ai_office.from_pshell as pshell
    import ai_office.from_vba as vba

    logfile = os.path.join(root_path, 'logs', 'ai_scan.log')
    logger = setup_logger('doc_processs', logfile)

#  定义字段的数据类型
data_types = {
    # 部分1 ：vba代码特征
    # 文档信息
    'file_name': 'category',
    'file_size': 'float32',

    # 函数名统计
    'autoopen': 'float32',  # 区分度极强
    'createobject': 'float32',  # 较强
    'getobject': 'float32',  # 区分度极强
    'windows': 'float32',  # 正负样本的区分度有限
    'array': 'float32',  # 区分度极强
    'environ': 'float32',  # 区分度极强

    'run': 'float32',  # 区分度较强
    'click': 'float32',  # 在所有函数名中出现的频率
    'close': 'float32',  # 在所有函数名中出现的频率
    'open': 'float32',  # 在所有函数名中出现的频率

    # 保证冗余，先不合并
    'workbook_open': 'float32',  # 区分度一般
    'document_open': 'float32',  # 区分度较强
    'document_close': 'float32',  # 区分度一般
    'auto_open': 'float32',  # 区分度一般
    'shell': 'float32',  # 指在函数名出现的频率
    'create': 'float32',  # 新增 10-12
    'files': 'float32',
    'ops': 'float32',
    'lines': 'float32',
    'prints': 'float32',
    # # TODO: 10-13新增 vba 变量名相关
    # 'var_good_ratio': 'float32',  速度太慢
    'var_digit_ratio': 'float32',
    'var_case_det': 'float32',
    'var_Axx_ratio': 'float32',
    'var_bad_num': 'float32',
    'var_too_long': 'float32',
    'var_skip_vb_num': 'float32',
    'var_else': 'float32',

    # 字符串变换
    'hex': 'float32',  # 区分度极强
    'chr': 'float32',  # 区分度较强
    'chrw': 'float32',  # 区分度极强
    'chrb': 'float32',  # 区分度极强
    'strreverse': 'float32',  # 区分度极强
    'xor': 'float32',  # 极强
    'cdate': 'float32',  # 区分度较强
    'cstr': 'float32',  # 区分度较强

    # 全局统计信息
    'math_ops': 'float32',
    'func_num': 'float32',
    'type_ops': 'float32',
    'str_ops': 'float32',

    # 部分2：olevba 工具
    'AutoExec': 'float32',
    'Suspicious': 'float32',
    'Base64 String': 'float32',  # 先在key里面找，没有就在suspicious里面找Base64
    'Hex String': 'float32',  # 先在key里面找，没有就在suspicious里面找Base64

    'Environ': 'float32',
    'Create': 'float32',
    'GetObject': 'float32',
    'Binary': 'float32',
    'System': 'float32',
    'Kill': 'float32',
    'Active': 'float32',  # ActiveWorkbook.SaveA 注意到关键字可能和工具版本相关，会变化
    'WScript.Shell': 'float32',
    'Powershell': 'float32',  # 在suspicious里面 先变化为小写
    'Call': 'float32',  # 在suspicious里面 call
    'VBHide': 'float32',  # 在suspicious里面 可能执行可执行文件
    'Print': 'float32',  # 在suspicious里面 print
    'VBA Stomping': 'float32',  # 在suspicious里面
    'Shell': 'float32',  # 在suspicious里面 含有shell就算 ShellExecute
    'ExecuteExcel4Macro': 'float32',
    'XMLhttp': 'float32',  # 含有就算
    'ShowWindow': 'float32',  # 测得时候考虑小写'ShowWindow',
    'Windows': 'float32',
    'Lib': 'float32',
    'Write': 'float32',
    'Output': 'float32',
    'Callbyname': 'float32',
    'Open': 'float32',
    'Put': 'float32',
    'File': 'float32',  # 在suspicious里面 带有file的
    'XLM macro': 'float32',  #
    'Execute': 'float32',

    # 增加IOC恶意程度
    'IOC': 'float32',  # key里面找IOC 可能含有http .exe .pif .bat .cmd .vbs .jar
    'ExeOrbat': 'float32',  # .exe .pif .bat .cmd
    'DDElink': 'float32',  # dde

    # 'MayBenign': 'float32',
    # 'MayMalicious': 'float32',
}


def get_features(file_path, train_mode=False, yara_all=False):
    features = {}

    # TODO: 记录过滤良性特征
    bengin_tag = ""
    # 0 olevba 识别 速度1s左右
    is_malicious, ole_features = ole.analyze_vbatool_result(
        file_path, train_mode)

    logger.info(
        f'is malicious: {is_malicious}. office vbatool scan {file_path}')
    if isinstance(ole_features, str):
        if is_malicious:
            return is_malicious, ole_features
        else:
            # 表明无可疑信息
            bengin_tag += 'no_sus_'

    # 1 手写检测PowerShell，这个把握最大 且最快
    if not train_mode:
        is_malicious, malicious_tag = pshell.analyze_ps_code(file_path)
        logger.info(
            f'is malicious: {is_malicious}. office powershell scan {file_path}')
        if is_malicious:
            return is_malicious, malicious_tag

    # TODO: 暂时不加入PowerShell额外特征
    # if features_tmp is None:
    #     print(f"{file_path} pshell解析失败")
    # else:
    #     features.update(features_tmp)

    if isinstance(ole_features, dict):
        features.update(ole_features)

    # 2 vbadump 识别 速度3s左右
    is_malicious, vba_features = vba.analyze_vba_code(file_path, train_mode)
    
    logger.info(
        f'is malicious: {is_malicious}. office vbadump scan {file_path}')

    if isinstance(vba_features, str):
        if is_malicious:
            return is_malicious,  vba_features
        else:
            bengin_tag += vba_features

    # TODO: 过滤VBA代码中特定的良性写法
    if bengin_tag.count('matlab') > 0:
        return False, "benign_matlab_vba"

    # TODO: 统一在最后形成df时 检查缺失值 设置默认
    if isinstance(vba_features, dict):
        features.update(vba_features)

    # TODO: yara识别的速度很慢 单个文件能到10 seconds
    if not train_mode:
        is_malicious,  malicious_tag = yara.analyze_yara_result(
            file_path, yara_all)
        logger.info(
            f'is malicious: {is_malicious}. office yara scan {file_path}')
        if is_malicious:
            return is_malicious, malicious_tag

    return is_malicious, features


def get_doc_df(features):
    # TODO:前面统一提取为空，即返回None，在这里统一填充缺失值
    for key, _ in data_types.items():
        features.setdefault(key, float(0.0))

    df = pd.DataFrame(features, index=[0])

    # Convert values in the dictionary to the appropriate data type
    for key, value in data_types.items():
        df[key] = df[key].astype(value)

    sorted_df = df.reindex(columns=sorted(df.columns))

    if 'file_name' in sorted_df.columns:
        sorted_df.drop(columns=['file_name'], inplace=True)

    return sorted_df


def scan_file(file_path):
    is_malicious, features = get_features(file_path)

    if is_malicious:
        print(f"存在恶意特征 {features}")
        return

    df = get_doc_df(features)
    model = tools.load_model()
    is_malicious = model.predict(df)[0]

    if is_malicious:
        print("恶意文件")
    else:
        print("良性文件")


def extract_doc_features(file_path, train_mode=False, yara_all=False):
    # TODO: 训练模式下，不允许短路退出
    is_malicious, merged_dict = get_features(file_path, train_mode, yara_all)

    # TODO: 短路开关 跳过哪些明显是恶意的文件 或 良性文件
    if isinstance(merged_dict, str):
        mal_tag = merged_dict
        return mal_tag

    return get_doc_df(merged_dict)


def extract_dir_features(file_dir: str, file_tag: str) -> None:
    """
    Extracts features from thousands of files and saves the data frame in parquet format.

    Args:
        file_dir (str): The directory containing the  files.
        file_tag (str): The tag to be included in the file name of the saved data frame.
    """
    start: int = 0
    dalta: int = 400  # Saves every dalta-th file
    total_amount: int = tools.count_files(file_dir)
    loop_num: int = int(total_amount / dalta) + 1

    for nth_loop in range(loop_num):
        save_path: str = f"data_frame/{file_tag}_{nth_loop:03d}.parquet"
        pd_list: List[pd.DataFrame] = tools.process_doc_files(
            file_dir, extract_doc_features, start, dalta)

        if len(pd_list) > 0:
            df: pd.DataFrame = pd.concat(pd_list, ignore_index=True)
            df.to_parquet(save_path, index=False)
            # print(f"loop {nth_loop} {file_path}")

        start += dalta


def test():
    # file_name = 'test\Mal_0002'
    file_name = r'data\Mal_err\Mal_err0128'
    ans = extract_doc_features(file_name)
    # print(ans)
    # print(f'{ans.info()}')

    # dir_path = 'data\Ben_edu'
    # tag = 'Ben_edu'
    # extract_dir_features(dir_path, tag)

    # 测试vba代码混淆 3s 左右
    # file_path = 'office_3k_data\Mal\Mal_0114'

    # 测试ole恶意执行软件 1s左右
    # file_path = 'office_3k_data\Mal\Mal_0514'

    # 测试PowerShell识别 0.45s
    # file_path = 'office_3k_data\Mal\Mal_1023'
    # ans = pshell.analyze_ps_code(file_path)
    # print(ans)

    # 测试Yara识别 10s
    # file_path = 'office_3k_data\Mal\Mal_0949'
    # ans = yara.analyze_yara_result(file_path)
    # print(ans)

    # 测试模型识别 3s
    # file_path = 'office_3k_data\Mal\Mal_0914'

    # ans = scan_file(file_path)
    # print(ans)


if __name__ == "__main__":
    tools.calculate_run_time(test)
