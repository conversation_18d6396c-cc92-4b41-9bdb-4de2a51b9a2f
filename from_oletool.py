'''
利用oletools提取特征，分几种情况：
1，有典型恶意特征 or 混淆特征 如IOC里面明显的 exe 文件
2，没有明显的混淆特征，提取特征并返回 特征

'''

import os
import sys

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    import ai_office.office_tools as tools


def analyze_vbatool_result(file_path, train_mode=False):
    is_malicious = False
    features_dict = tools.parse_vba_table(file_path)

    # print(features_dict)

    # TODO: 需要进一步检测短路时机
    if isinstance(features_dict, str):
        return False, 'benign_no_suspicious'

    if not train_mode:
        # 1 典型恶意特征 混淆特征
        is_malicious, mal_tag = detect_malicious_signs(
            features_dict, file_path)
        if is_malicious:
            return is_malicious, mal_tag

    # 2 没有明确特征，提取模糊特征
    features = {}
    features = extract_oletable_features(features_dict)
    ddelink = tools.get_dde_link(file_path)  # TODO:需要修正，去除DDE内部的混淆，提取恶意特征

    # TODO: 这里也可能存在混淆但是样本不够，待增强
    if ddelink and ('cmd.exe' in ddelink or 'powershell' in ddelink):
        features['DDElink'] = 1
    else:
        features['DDElink'] = 0

    return is_malicious, features


def detect_malicious_signs(features_dict, doc_path):
    # 1 恶意特征 IOC 中含有恶意可执行文件
    # key里面找IOC 可能含有 'exe', 'bat', 'cmd','dll'
    # 'putty.exe' 'shell32.dll' 'wininet.dll' 打开网络接口 'gswin32c.exe' 打开任务管理器
    # 'ExpToOWS.dll'
    # 应该过滤 'excel.exe' 'winmm.dll'
    keys = ['exe', 'bat', 'cmd', 'dll']
    exeOrbat = 0

    if "IOC" in features_dict.keys():
        # print('features_dict:', features_dict['IOC'])
        iocs = list(features_dict['IOC'])
        white_list = ['excel.exe', 'winmm.dll', 'kernel32.dll', 'Solver32.dll']
        for key in white_list:
            if key in iocs:
                iocs.remove(key)
        for key in keys:
            exeOrbat += tools.contain_key_frequency(key, iocs)
        if exeOrbat > 1:
            print('exeOrbat:', features_dict['IOC'])
            return True, 'ioc_exe_found'

        # 1.2 TODO:模糊特征，暂时不能完全http确定是恶意的，需要恶意网址分析工具
        # http = tools.contain_key_frequency('http', features_dict['IOC'])
        # if http > 0:
        #     with open('log/ioc_http.txt', 'a+') as f:
        #         f.write(str(features_dict['IOC']))
        #         f.write('\n')

        #     return True, 'ioc_http_found'

    # 2 恶意特征'AutoExec' 'Suspicious' 数目大 TODO: 需要更大的数据量锁定阈值
    if "AutoExec" in features_dict.keys() and "Suspicious" in features_dict.keys():
        AutoExec = len(features_dict['AutoExec'])
        Suspicious = len(features_dict['Suspicious'])
        if AutoExec + Suspicious > 40:
            vba_stomping = tools.get_vba_stomp(doc_path)
            if vba_stomping:
                return True, 'mal_autoexec_found'

    # 3 VBA stomping
    if "Suspicious" in features_dict.keys():
        if 'VBA Stomping' in features_dict['Suspicious']:
            # print(features_dict['Suspicious'])
            return True, 'vba_stomping_found'

    return False, 'good'


def extract_oletable_features(features_dict):
    ''' 测试
    features_dict ={
        'Suspicious':[ 'Create', 'CreateObject',  'ExecuteExcel4Macro'],
        'Base64 String':["wefo","we"],
        'Hex String':["wfeo","we"],
        'IOC':['.exe','.cmd','.bat','.pif','.vbs','.jar'],
    }
    '''

    AutoExec = 0
    if 'AutoExec' in features_dict.keys():
        AutoExec = len(features_dict['AutoExec'])

    Suspicious = 0
    if 'Suspicious' in features_dict.keys():
        Suspicious = len(features_dict['Suspicious'])
    else:
        features_dict['Suspicious'] = []

    # 记录Base64字符串个数
    Base64_String = 0
    if "Base64 String" in features_dict.keys():
        Base64_String = len(features_dict['Base64 String'])
    else:
        Base64_String = tools.contain_key_frequency(
            'Base64', features_dict['Suspicious'])

    Hex_String = 0
    if "Hex String" in features_dict.keys():
        Hex_String = len(features_dict['Hex String'])
    else:
        Hex_String = tools.contain_key_frequency(
            'Hex', features_dict['Suspicious'])

    Environ = tools.count_total_frequency(
        ['Environ'], features_dict['Suspicious'])

    Create = tools.contain_key_frequency(
        'Create', features_dict['Suspicious'])
    GetObject = tools.count_total_frequency(
        ['GetObject'], features_dict['Suspicious'])

    Binary = tools.count_total_frequency(
        ['Binary'], features_dict['Suspicious'])
    system = tools.contain_key_frequency(
        'system', features_dict['Suspicious'])
    Kill = tools.contain_key_frequency('kill', features_dict['Suspicious'])

    Active = tools.contain_key_frequency(
        'Active', features_dict['Suspicious'])

    WScript_Shell = tools.contain_key_frequency(
        'WScript.Shell', features_dict['Suspicious'])

    Powershell = tools.contain_key_frequency(
        'powershell', features_dict['Suspicious'])

    Call = tools.contain_key_frequency('call', features_dict['Suspicious'])
    vbHide = features_dict['Suspicious'].count('vbHide')
    Print = tools.contain_key_frequency('print', features_dict['Suspicious'])
    VBA_Stomping = tools.contain_key_frequency(
        'VBA Stomping', features_dict['Suspicious'])
    Shell = tools.contain_key_frequency('shell', features_dict['Suspicious'])
    ExecuteExcel4Macro = tools.count_total_frequency(
        ['ExecuteExcel4Macro'], features_dict['Suspicious'])

    xmlhttp = tools.contain_key_frequency(
        'xmlhttp', features_dict['Suspicious'])
    showwindow = tools.contain_key_frequency(
        'showwindow', features_dict['Suspicious'])

    Windows = tools.contain_key_frequency(
        'windows', features_dict['Suspicious'])

    Lib = tools.count_total_frequency(['lib'], features_dict['Suspicious'])
    Write = tools.count_total_frequency(['write'], features_dict['Suspicious'])
    Output = tools.count_total_frequency(
        ['output'], features_dict['Suspicious'])
    callbyname = tools.count_total_frequency(
        ['callbyname'], features_dict['Suspicious'])
    Open = tools.count_total_frequency(['open'], features_dict['Suspicious'])
    Put = tools.count_total_frequency(['put'], features_dict['Suspicious'])
    File = tools.contain_key_frequency('file', features_dict['Suspicious'])
    XLM_macro = tools.contain_key_frequency(
        'XLM macro', features_dict['Suspicious'])
    Execute = tools.contain_key_frequency('exec', features_dict['Suspicious'])

    IOC = 0
    if "IOC" in features_dict.keys():
        IOC = len(features_dict['IOC'])
    else:
        IOC = tools.contain_key_frequency('ioc', features_dict['Suspicious'])

    # key里面找IOC 可能含有http .exe .pif .bat .cmd .vbs .jar
    keys = ['exe', 'bat', 'cmd', 'pif', 'vbs', 'jar']
    exeOrbat = 0
    if "IOC" in features_dict.keys():
        for key in keys:
            exeOrbat += tools.contain_key_frequency(key, features_dict['IOC'])

    features = {
        'AutoExec': AutoExec,
        'Suspicious': Suspicious,
        'Base64 String': Base64_String,
        'Hex String': Hex_String,
        'Environ': Environ,
        'Create': Create,
        'GetObject': GetObject,
        'Binary': Binary,
        'System': system,
        'Kill': Kill,
        'Active': Active,
        'WScript.Shell': WScript_Shell,
        'Powershell': Powershell,
        'Call': Call,
        'VBHide': vbHide,
        'Print': Print,
        'VBA Stomping': VBA_Stomping,
        'Shell': Shell,
        'ExecuteExcel4Macro': ExecuteExcel4Macro,
        'XMLhttp': xmlhttp,
        'ShowWindow': showwindow,
        'Windows': Windows,
        'Lib': Lib,
        'Write': Write,
        'Output': Output,
        'Callbyname': callbyname,
        'Open': Open,
        'Put': Put,
        'File': File,
        'XLM macro': XLM_macro,
        'Execute': Execute,
        'IOC': IOC,
        'ExeOrbat': exeOrbat,

    }

    return features


def test():
    # D:\office_analyze\identity_office\office_all_data\Mal_err\Mal_err0007
    file_path = 'D:\office_analyze\identity_office\office_all_data\Mal_err\Mal_err0415'
    file_path = r"C:\Users\<USER>\Desktop\Stamps\pdb.doc"
    is_malicious, features = analyze_vbatool_result(file_path)
    print(is_malicious)
    print(features)


if __name__ == "__main__":
    tools.calculate_run_time(test)
