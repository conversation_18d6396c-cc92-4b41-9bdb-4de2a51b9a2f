import "math"

// see the relevant posts at:
//     http://blog.inquest.net/blog/2019/01/29/Carving-Sneaky-XLM-Files/
//     https://inquest.net/blog/2020/03/18/Getting-Sneakier-Hidden-Sheets-Data-Connections-and-XLM-Macros
//     https://inquest.net/blog/2020/05/06/ZLoader-4.0-Macrosheets-
// see XLMDeobfuscator at:
//     https://github.com/InQuest/XLMMacroDeobfuscator/ (fork from @DissectMalware)

rule excel40_hunter
{
    strings:
        $ole_marker       = {D0 CF 11 E0 A1 B1 1A E1}
        $velvet_sweatshop = {2F 00 06 00 00 00 59 B3 0A 9A}
        $macro_sheet_h0   = "Excel 4.0 Macros"
        $macro_sheet_h1   = {85 00 ?? ?? ?? ?? ?? ?? 01 01}
        $macro_sheet_h2   = {85 00 ?? ?? ?? ?? ?? ?? 02 01}
        $macro_sheet_h3   = {85 00 ?? ?? ?? ?? ?? ?? ?? 01 01}
        $macro_sheet_h4   = {85 00 ?? ?? ?? ?? ?? ?? ?? 02 01}
        $url              = /https?:\/\/[\w\/\.\-]+/ nocase ascii wide
        $dconn            = /\x76\x08\x00\x00\x04\x00[\x40-\x7f\xc0-\xff]/
        $magic_xlsx       = /^\x50\x4B\x03\x04/
        $anchor_xlsx      = /macrosheets\/[a-z0-9_-]+\.[a-z]+PK/ nocase

    condition:
        new_file and
        (
            ($ole_marker in (0..1024) and (1 of ($macro_sheet_h*) or ($url and $dconn) or $velvet_sweatshop or math.entropy(0, filesize) >= 7.5))
            or
            ($magic_xlsx at 0 and $anchor_xlsx)
        )
}
