rule ClamAV_Emotet_String_Aggregate
{
    meta:
        Author      = "InQuest Labs"
        URL         = "https://github.com/InQuest/yara-rules"
        Description = "A pruned aggregate of all Emotet related strings extracted from ClamAV on 2019-07-03."

    strings:
        $ = "\"A\", \"w\", \"E\", \"C\", \"j\", \"d\", \"Fc\", \"I\", _"
        $ = "\"B\", \"P\", \"wW\", \"Z\", \"iw\", \"UH\", \"c\", \"F\")"
        $ = "\"DR\", \"i\", \"O\", \"w\", \"O\", \"W\", \"Ju\", \"l\", _"
        $ = "\"E\", \"vr\", \"d\", \"h\", \"z\", \"O\", \"k\", \"Fm\", _"
        $ = "\"EG\", \"v\", \"aF\", \"L\", \"i\", \"s\", \"qN\", \"KK\", _"
        $ = "\"Er\", \"rG\", \"Pn\", \"vW\", \"JJ\", \"h\", \"Kj\", \"Ah\", _"
        $ = "\"V\", \"oT\", \"l\", \"hn\", \"EE\", \"Hp\", \"jT\", \"r\", _"
        $ = "\"Zr\", \"wq\", \"V\", \"V\", \"a\", \"R\", \"S\", \"m\", _"
        $ = "\"co\", \"ol\", \"qw\", \"W\", \"K\", \"ZX\", \"V\", \"kM\", _"
        $ = "\"ip\", \"I\", \"Dk\", \"z\", \"I\", \"H\", \"Ko\", \"q\")"
        $ = "\"q\", \"Pk\", \"Bz\", \"w\", \"XT\", \"Sr\", \"B\", \"zu\", _"
        $ = "\"qz\", \"W\", \"O\", \"jB\", \"G\", \"m\", \"Wc\", \"c\", _"
        $ = "\"s\", \"mA\", \"Ql\", \"ln\", \"C\", \"wG\", \"l\", \"iu\", _"
        $ = "\"v\", \"W\", \"c\", \"nm\", \"R\", \"Za\", \"SH\", \"Y\", _"
        $ = "\"zZ\", \"n\", \"B\", \"Rd\", \"zb\", \"zw\", \"S\", \"To\")"
        $ = "BbLaumnc = Sqr(297301610 / CSng(84122969 - Cos(37521102 - 276382110) + jArhKK + Rnd(278702609 - 22664827)))"
        $ = "CjONpTOBY = NkdjH"
        $ = "EimOtQW = fkipfviwF"
        $ = "GPYKuJmPN = CDate(KROMo)"
        $ = "GSwdf = Hex(pZRQKYfG)"
        $ = "GbzcjjsOu = 57201732"
        $ = "GqMKodDv = CDate(GKtvO)"
        $ = "HkWVBQV = Oct(JiJAvfM)"
        $ = "HtAbZ = Int(161244399 * jTBFrt)"
        $ = "JaiAhE = CDate(283733543)"
        $ = "KbYRtc = CDate(PTGTls)"
        $ = "LfRjb = Sqr(305831286 / CSng(76527194 - Cos(189554916 - 4228452) + CVSjZwckw + Rnd(255191871 - 82381179)))"
        $ = "Lodma = Sqr(158611452 / CSng(53399989 - Cos(228303053 - 164016825) + SlZNGGPBm + Rnd(77667323 - 270228088)))"
        $ = "MzQnuKYi = Sqr(224971823 / CSng(272274480 - Cos(303612726 - 72935234) + TKtLB + Rnd(294479071 - 259499898)))"
        $ = "NvGwwkV = Hex(BEjDXd)"
        $ = "PHFllzzhd = CDate(336713111)"
        $ = "PXvTHzt = Sqr(128431312 / CSng(233496515 - Cos(35314610 - 292196268) + bwWOWw + Rnd(118649096 - 251935681)))"
        $ = "QNbziqW = Sqr(158805557 / CSng(331003561 - Cos(249131549 - 186665267) + aRQQu + Rnd(267242902 - 158055032)))"
        $ = "UUJpKPIH = 280449576"
        $ = "VlrZSE = 299451022"
        $ = "XZtzA = qvCHUwVZO"
        $ = "YHWXG = 150069403"
        $ = "ZRapb = 64895411"
        $ = "ZWDJk = Hex(jbhukX)"
        $ = "ZhUrIji = Int(118016430 * tUEpB)"
        $ = "ZhZlvfXG = CDate(IofMUlj)"
        $ = "aVzNNi = CLng(57406342)"
        $ = "batQK = 303397753"
        $ = "bnmLiEQ = CDate(39447712)"
        $ = "cJAjYZjMI = 281370206"
        $ = "hlhjKc = mtfQh"
        $ = "hrIdd = CLng(151925004)"
        $ = "iobZdiJ = ChrW(159326751)"
        $ = "jiWvdw = 233631985"
        $ = "kjrUz = ChrW(25661779)"
        $ = "lVzOp = iIDfQWBY"
        $ = "lmYcoF = CDate(jbAfYLdM)"
        $ = "mPHwrFSM = CDate(KFAdkRt)"
        $ = "mSbTWQh = CDate(198464871)"
        $ = "mcPSLjlC = Int(22534269 * bUKsQ)"
        $ = "mwpmbE = abhBLvRE"
        $ = "nTNrqV = CDate(SUzXWuZm)"
        $ = "noOjltQ = Oct(aoWXjNBOw)"
        $ = "ocsLjZ = wWWvwT"
        $ = "ohFjViK = CLng(94026124)"
        $ = "qMCQMQP = Oct(jUipORjds)"
        $ = "qTfbQTYSq = 241712008"
        $ = "qwZwJjK = Sqr(81091076 / CSng(214691539 - Cos(2246847 - 11439174) + dILFMdpS + Rnd(84892047 - 176965475)))"
        $ = "rAcul = dfhMTK"
        $ = "skEjlXnf = 320215791"
        $ = "tIXNtk = Sqr(240178619 / CSng(301643513 - Cos(31914199 - 192782238) + YQFirjuqi + Rnd(111920305 - 25450526)))"
        $ = "tIiiWaz = dKMBcjniu"
        $ = "tWpMw = CLng(43829233)"
        $ = "tilquXtzk = 266433336"
        $ = "uFZka = jEjZqA"
        $ = "vBzpGKc = Hex(AmafXFSL)"
        $ = "wFTzivB = Hex(rPOuRKXU)"
        $ = "wjFJo = Hex(TlDojE)"
        $ = "zvOIvIQ = 186460771"
        $ = "BjiMS = 309251431 + Oct(173155768) - 283161521 - CBool(335825026 / 132218507) * 55370527 + Log(nLuUjMFGu - CLng(308671783)) - 134483356 + Hex(VzmWF)"
        $ = "Case 106092274"
        $ = "Case 11454302"
        $ = "Case 143120012"
        $ = "Case 154779694"
        $ = "Case 170116777"
        $ = "Case 189594986"
        $ = "Case 205744771"
        $ = "Case 228088127"
        $ = "Case 233192990"
        $ = "Case 247483045"
        $ = "Case 252250112"
        $ = "Case 264557124"
        $ = "Case 266083784"
        $ = "Case 276573924"
        $ = "Case 278426237"
        $ = "Case 278864415"
        $ = "Case 332550632"
        $ = "Case 336599014"
        $ = "Case 337721066"
        $ = "Case 341340570"
        $ = "Case 38445923"
        $ = "Case 44630363"
        $ = "Case 457529"
        $ = "Case 52629287"
        $ = "Case 54855370"
        $ = "Case 71914715"
        $ = "Case 72734952"
        $ = "Case 83662727"
        $ = "Case 92027715"
        $ = "Case 94959251"
        $ = "UwFuzNw = Sgn(CXIrlX)"
        $ = "dOrsk = Sgn(wutduKITM)"
        $ = "hZBzQjPcR = uamCCaJ"
        $ = "onUqBpwLN = 82127321 + Oct(182722426) - 318417404 - CBool(161630097 / 169773907) * 46426675 + Log(BkzCQl - CLng(115674427)) - 290082511 + Hex(iCcTIhG)"
        $ = "If lLpXknlEZ Xor krikqDT Then"
        $ = "Select Case AHHLF"
        $ = "Select Case VLdwEK"
        $ = "Select Case YOThc"
        $ = "Select Case ZDjQpzlT"
        $ = "Select Case oErsqBA"
        $ = "Select Case wMoAPPbB"
        $ = "Error IVdTTI + JqPUSU + bKEGNY + fSEHr * wUsTKJ + wYNAN + (bDUMEp + ZwMGp)"
        $ = "Error hdvow + tphap - rozVTT + WvVwc + nwkhi + cinTH - YnHlpB + DVdMQ - fAtXdM + TcJZIH + TMwDV + jvfMkF"
        $ = "Error hojRZ + fYZMt / kLuORO + qLTjd + IPofAc + Gbpvz - (fjmOS + waziDJ + CWwzOF + iQKqt + (kjlJW + PGipEw))"
        $ = "FormatNumber MaUVi + QlPhT / (qlqsQs + ttIHB + QZafUt + QKnwYp * (PhInFI + dWKlTz + taETS + ScpGun))"
        $ = "HOzldP = 33235 * dAojBv + 22013 / nLEvn"
        $ = "HOzldP = 98772 * nOWSp + sBlTo - asjVO"
        $ = "HOzldP = Log(7)"
        $ = "IsArray 25855 + rdDbP + tNCwEo - hRPpw"
        $ = "TimeValue (KUTwat + BihLSP * (TRjIO + IBzmq + (lbcHr + hEpPzZ)))"
        $ = "TimeValue (NlbKir + LudNjh) / JEjSDi + WtKSQ + uCnbbT + BikpW"
        $ = "TimeValue zuUbIw + lVBtJ / DPtBvm + kjcFG + nrBsS + uwYPRr"
        $ = "VarType LCase(68)"
        $ = "VarType Rnd(685)"
        $ = "XJwIVwuI = (obXlRcwfG - CDbl(20641839) / RJDio + Sgn(254461154)) - 139223241 + CInt(qiTDjZlsK) - 59679732 * Fix(53539363 * Oct(DJvEpFnD))"
        $ = "fWWvfEqH = Array(\"T\", \"zr\", \"J\", \"FM\", \"UX\", \"ho\", \"v\", \"m\", _"
        $ = "For Each OwqOz In fzGZRzND"
        $ = "For Each WjnHrtjz In twtiSZwQc"
        $ = "For Each jBwnIABM In TZwiwXp"
        $ = "& fhc1OBWLx _"
        $ = "& q1z5K _"
        $ = "* 971021366 / 369160997 + Log(EQQo_Q_"
        $ = "* CDate(RAAA_c * ChrW(789459752 / CDate(ZQQABoD)))) _"
        $ = "+ \"mts:w\" + \"in32\" + \"_proc\" _"
        $ = "+ \"n\" + \"mg\" _"
        $ = "+ \"ocess\" + \"S\" + \"tartup"
        $ = "+ \"s:Wi\" + \"n3\" + \"2_Pr\" _"
        $ = "+ \"tartup"
        $ = "+ (\"264\") + (\"VUcid9b\" + (\"587\" + \"45\") + \"GNB7qA2\" + (\"PN2ailr0"
        $ = "+ (\"722\") + (\"ZBDvJzoR\" + (\"755\" + \"879\") + \"sa34d4\" + (\"d2iIdSN"
        $ = "+ (\"788\") + (\"hPlD3NoI\" + (\"757\" + \"842\") + \"IwZKKI6\" + (\"i7C2YT"
        $ = "+ (i089968) + u46626 _"
        $ = "+ (i1711884) + Y241176 _"
        $ = "+ (l__597) + c0_459 _"
        $ = "+ (m2388692) + L447996 _"
        $ = "+ (t_423055) + D017109 _"
        $ = "+ Int(L42114) + u480484 + Int(968) _"
        $ = "+ V_45800 + Int(674) + u598_083 + h274_2_ + 325 + E2_46145"
        $ = "+ Y9663271 + Int(245) + s351563 + K_3097 + 166 + O098_73"
        $ = "+ u24154 + Int(275) + f05_054 + V_0385 + 167 + Q8_255"
        $ = "+ z678286 + 337485637"
        $ = "- CBool(744026654) / KUADQ_Ax - _"
        $ = ".Print \"167\" _"
        $ = ".Print \"235\" _"
        $ = ".Print \"910\" _"
        $ = ".Shell(iihfU, jChzFM), YXtwPRj)"
        $ = "0,18,50,35"
        $ = "0:4174747269627574652056425f4e616d65203d202241617a4446757a22"
        $ = "0:4174747269627574652056425f4e616d65203d2022424e43617a66466a755275704422"
        $ = "0:4174747269627574652056425f4e616d65203d20224843484d746c465a6a22"
        $ = "0:4174747269627574652056425f4e616d65203d20224e745657704e4461634c58666b6922"
        $ = "0:4174747269627574652056425f4e616d65203d20224f7244744c5873556a69436c7022"
        $ = "0:4174747269627574652056425f4e616d65203d2022566c6a57584a7a22"
        $ = "0:4174747269627574652056425f4e616d65203d2022576948535866466b7a5922"
        $ = "0:4174747269627574652056425f4e616d65203d202257695a7a4444444f6b7722"
        $ = "0:4174747269627574652056425f4e616d65203d2022586750524630333522"
        $ = "0:4174747269627574652056425f4e616d65203d2022685951554557767470706122"
        $ = "0:4174747269627574652056425f4e616d65203d20226d48414e6b7a704d22"
        $ = "0:4174747269627574652056425f4e616d65203d20226d547761564f777422"
        $ = "0:4174747269627574652056425f4e616d65203d20227676487a705664516d6c50536a22"
        $ = "0a20a7ja270E70"
        $ = "100129994"
        $ = "106885034"
        $ = "109593058"
        $ = "116477639"
        $ = "11661310"
        $ = "117793057"
        $ = "118581505"
        $ = "119120316"
        $ = "120193573"
        $ = "122995335"
        $ = "126278805"
        $ = "126p126X4"
        $ = "131182809"
        $ = "146151880"
        $ = "152501303"
        $ = "153272416"
        $ = "155512407"
        $ = "16127587"
        $ = "173392967"
        $ = "180521468"
        $ = "181567594"
        $ = "188643686"
        $ = "191) + iPhMlbkN"
        $ = "19;26;43"
        $ = "207926327"
        $ = "208231696"
        $ = "209266480"
        $ = "213075158"
        $ = "21443318"
        $ = "22,22,41"
        $ = "220246866"
        $ = "22P108x1"
        $ = "23292253"
        $ = "233440611"
        $ = "235666267"
        $ = "237268900"
        $ = "243561327"
        $ = "248196837"
        $ = "253504504"
        $ = "255442848"
        $ = "257199540"
        $ = "264051008"
        $ = "269610872"
        $ = "270528015"
        $ = "280524880 - hAAAQAXB / wQABUUQx - Tan(118770473"
        $ = "285250695"
        $ = "286329143"
        $ = "294542035"
        $ = "295329648"
        $ = "307021049"
        $ = "307129179"
        $ = "309748135"
        $ = "310325231"
        $ = "316457819"
        $ = "32,27,60"
        $ = "322065214"
        $ = "326063447"
        $ = "32_Process\")).Create# LDG3FL, Czl1lInR, rksq5icm, wWNvOivN"
        $ = "333127255"
        $ = "335333058"
        $ = "357573364"
        $ = "359022593"
        $ = "362224930"
        $ = "394101855"
        $ = "394595464"
        $ = "401059716"
        $ = "427528300"
        $ = "434653231"
        $ = "441773021"
        $ = "446238501"
        $ = "451942070"
        $ = "466952686"
        $ = "46;41;77;7"
        $ = "47;71;8;33"
        $ = "481272899"
        $ = "481768994"
        $ = "483656317"
        $ = "496888487"
        $ = "4E69!76!1k"
        $ = "505287444"
        $ = "511869992"
        $ = "531109272"
        $ = "55599325"
        $ = "565231638"
        $ = "568842327"
        $ = "571\" + (\"844\") + (\"XUrtUowo\" + (\"208\" + \"85\") + \"dAf8Nd\" + (\"VS2cAhuq"
        $ = "588854775"
        $ = "590745234"
        $ = "593798217"
        $ = "595449481"
        $ = "5o12o8_95"
        $ = "607085817"
        $ = "630\" + (\"152\") + (\"MDCaz7n\" + (\"879\" + \"953\") + \"UwH4WoR\" + (\"jXh9PBV"
        $ = "631583992"
        $ = "636310124"
        $ = "644) + cYGHYb"
        $ = "645065928"
        $ = "64551552 * Hex(930023985 * _"
        $ = "659489749"
        $ = "65;65;65"
        $ = "662824139"
        $ = "66728700"
        $ = "675223502"
        $ = "685213722"
        $ = "69,62,22"
        $ = "695897279 - tAAcBA / BDQAAw - Tan(822702749"
        $ = "6;8;36;75"
        $ = "71,65,15"
        $ = "71231191"
        $ = "716658540"
        $ = "73222753"
        $ = "739469662"
        $ = "748\" + (\"853\") + (\"R4oDMqO\" + (\"737\" + \"650\") + \"CtwNJu\" + (\"IVOMH0wn"
        $ = "75102559"
        $ = "754677693"
        $ = "768003727"
        $ = "77665428"
        $ = "78023260"
        $ = "78098758"
        $ = "787669236"
        $ = "799149985"
        $ = "7_124J99"
        $ = "804572183"
        $ = "807356145"
        $ = "81,29,68"
        $ = "813357237"
        $ = "818673481"
        $ = "82136797"
        $ = "82981355"
        $ = "841074043"
        $ = "84471752"
        $ = "851226700"
        $ = "875954121 / Sgn(737941199) * (AAAUAAZ + CVar(520001770"
        $ = "8>3_97K11"
        $ = "90191876"
        $ = "911374040"
        $ = "95552863"
        $ = "957157475"
        $ = "97457890"
        $ = "975199089"
        $ = "977541618"
        $ = "982295035"
        $ = "994\" + (\"770\") + (\"ikqIW9jY\" + (\"410\" + \"559\") + \"o_uUQO\" + (\"Sc7z76"
        $ = "998318633"
        $ = "= 729503958 - _"
        $ = "= 81793 + Atn(62659) / 92776 / _"
        $ = "= CStr(LAAABAx + 888042072 + 920531570 _"
        $ = "= Hex(402514940"
        $ = "= aBkkQC"
        $ = "A0523292"
        $ = "A482367_K77951q140_878V3_462"
        $ = "A4AAXUD = 181341305 - ChrB(2559126 * Round(651620675) + WUQAkw - ChrB(ABkA1oGQ)) / nAxkAUo / Rnd(663640487 / QDDk4A * SpBb / Chr"
        $ = "AAAAAD41"
        $ = "AAAUAUDD"
        $ = "ADowIECwu"
        $ = "AEbWwmZGAjzBAhzHwhXrGow"
        $ = "AFzwbGAj"
        $ = "ALFlHisskZra"
        $ = "APpZnx9g(11) = \"byIpQt1Os"
        $ = "AQXAAUoB"
        $ = "AQwuqitCS = lBGnjRIG + azvQYmSjic + nlBSwazY + kGSmjCdA + qjcdjvG + IfzvP + KSTCWjRT"
        $ = "ASABBAHIA"
        $ = "A_3_68_ = (190967908"
        $ = "ActiveDocument.BuiltInDocumentProperties(\"Comments\")"
        $ = "AiHuiEuq"
        $ = "AmVqNCn7"
        $ = "AppActivate 224602168"
        $ = "AppActivate 263"
        $ = "AppActivate 626"
        $ = "AppActivate CDbl(955"
        $ = "AppActivate CSng(28893 + KzluMZ"
        $ = "AppActivate ChrB(JGYhJa"
        $ = "AppActivate Hex(8037"
        $ = "AppActivate Hex(NoPWBD"
        $ = "AppActivate Round(bwBYFw"
        $ = "AppActivate Sgn(QTcJBp"
        $ = "AppActivate mKiIIo"
        $ = "AppActivate wBPlM"
        $ = "Asc(MDQAZx"
        $ = "Asc(RAc1XDD"
        $ = "Asc(iDGwwA"
        $ = "Atn(312653131"
        $ = "Atn(58212323"
        $ = "Atn(60148"
        $ = "Atn(614474390"
        $ = "Atn(627266553"
        $ = "Atn(676909037"
        $ = "Atn(767735314"
        $ = "Atn(779968282"
        $ = "Atn(967875984"
        $ = "Atn(BG_A_AB"
        $ = "Atn(VXLmLb"
        $ = "Atn(fcSsNbq"
        $ = "Atn(sNOVcw"
        $ = "Attribute VB_Base = \"0{0A5FB00E-D718-41CC-9F16-C8FEF46C3ACD}{61BC3D54-1463-4DFA-B77B-B2799E590BA5}"
        $ = "Attribute VB_Base = \"0{0B7760E4-24B1-4EA8-83E8-7C8120774294}{4EE64FEA-4D69-4EC0-A65A-16BDDD64BD2E}"
        $ = "Attribute VB_Base = \"0{8411D7C6-E63B-407B-BD8C-F3BBE1DDA996}{5FCC6A50-C6BB-4371-98C5-75CC1C702E38}"
        $ = "Attribute VB_Base = \"0{8C95744E-5F3D-487F-964B-2747704AF649}{F4630A7E-3C1F-4513-B4F7-B4DE18C5AB21}"
        $ = "Attribute VB_Base = \"0{9F8B55CD-B6F0-4DA4-AB50-70E9C7FA2034}{5B07E719-5147-4613-976E-888249BCE951}"
        $ = "Attribute VB_Base = \"0{C1A5688F-8A34-49B0-8F55-EFF4989335E6}{FDAD0313-DC72-4D8D-8329-E9DB31704FB0}"
        $ = "Attribute VB_Base = \"0{DBF9D248-A1E3-4610-B915-D5E26924687C}{144EA630-6A64-4AA4-8696-DAE616E571E6}"
        $ = "Attribute VB_Base = \"0{E1349350-99E0-4A72-90F3-1977DB684D66}{83EDA91E-4E8B-4958-96BB-65346C640174}"
        $ = "Attribute VB_Base = \"0{FB6352A4-015F-40AE-940E-DFEF496155BC}{6A887CBC-6F52-456C-997C-2C3C6AC55011}"
        $ = "Attribute VB_Control = \"A0523292, 5, 5, MSForms, TextBox"
        $ = "Attribute VB_Control = \"B7zidj, 2, 2, MSForms, ComboBox"
        $ = "Attribute VB_Control = \"BbHdEn, 1, 1, MSForms, ComboBox"
        $ = "Attribute VB_Control = \"E606327, 1, 1, MSForms, TextBox"
        $ = "Attribute VB_Control = \"H_226448, 2, 2, MSForms, TextBox"
        $ = "Attribute VB_Control = \"P8jBzNa, 0, 0, MSForms, ComboBox"
        $ = "Attribute VB_Control = \"RCtXzHqR, 1, 1, MSForms, TextBox"
        $ = "Attribute VB_Control = \"SZS63zu, 0, 0, MSForms, TextBox"
        $ = "Attribute VB_Control = \"X55394, 4, 4, MSForms, TextBox"
        $ = "Attribute VB_Control = \"awsaanQB, 0, 0, MSForms, ComboBox"
        $ = "Attribute VB_Control = \"b1jOhv, 1, 1, MSForms, TextBox"
        $ = "Attribute VB_Control = \"kmi5rho, 2, 2, MSForms, ComboBox"
        $ = "Attribute VB_Control = \"mSRp5U, 0, 0, MSForms, ComboBox"
        $ = "Attribute VB_Control = \"n00dj7, 1, 1, MSForms, ComboBox"
        $ = "Attribute VB_Control = \"n928_112, 0, 0, MSForms, TextBox"
        $ = "Attribute VB_Control = \"pMW1ir5, 2, 2, MSForms, ComboBox"
        $ = "Attribute VB_Control = \"qzKwvQ, 2, 2, MSForms, ComboBox"
        $ = "Attribute VB_Control = \"rn51tfI, 0, 0, MSForms, ComboBox"
        $ = "Attribute VB_Control = \"sjQaAaUI, 0, 0, MSForms, ComboBox"
        $ = "Attribute VB_Control = \"zM8ksqra, 0, 0, MSForms, TextBox"
        $ = "Attribute VB_Name = \"B8807374"
        $ = "Attribute VB_Name = \"BHBYZjiEINI\""
        $ = "Attribute VB_Name = \"C93518"
        $ = "Attribute VB_Name = \"FAZwXB"
        $ = "Attribute VB_Name = \"GAD_DG"
        $ = "Attribute VB_Name = \"GcwzTqwMFhwucu\""
        $ = "Attribute VB_Name = \"HCHMtlFZj\""
        $ = "Attribute VB_Name = \"LkjIKwSUwwkTnX"
        $ = "Attribute VB_Name = \"MHcfFkjZ\""
        $ = "Attribute VB_Name = \"Marketing96"
        $ = "Attribute VB_Name = \"P324_802"
        $ = "Attribute VB_Name = \"PxAAAXA"
        $ = "Attribute VB_Name = \"Q98384"
        $ = "Attribute VB_Name = \"RH76qr"
        $ = "Attribute VB_Name = \"SI6wbVU3"
        $ = "Attribute VB_Name = \"TWsSKiaVwtTDK\""
        $ = "Attribute VB_Name = \"TznFGdWSDrwjIf\""
        $ = "Attribute VB_Name = \"WZbIoOvCY"
        $ = "Attribute VB_Name = \"bOX8wu"
        $ = "Attribute VB_Name = \"baO5AQ7"
        $ = "Attribute VB_Name = \"bwiOniizVBh\""
        $ = "Attribute VB_Name = \"cGDABAQ"
        $ = "Attribute VB_Name = \"hAAGB_A"
        $ = "Attribute VB_Name = \"iDXC_1"
        $ = "Attribute VB_Name = \"ihVETJiVClin"
        $ = "Attribute VB_Name = \"jWKEYwOmE\""
        $ = "Attribute VB_Name = \"kSSkRJvc"
        $ = "Attribute VB_Name = \"kztzzHWYZ\""
        $ = "Attribute VB_Name = \"lVr36URi"
        $ = "Attribute VB_Name = \"mAMYwODSErvVfz\""
        $ = "Attribute VB_Name = \"rCQAkUA"
        $ = "Attribute VB_Name = \"s7319992"
        $ = "Attribute VB_Name = \"ulwMK8UL"
        $ = "Attribute VB_Name = \"w1578953"
        $ = "Attribute VB_Name = \"w999057"
        $ = "Attribute VB_Name = \"wADZAQAQ"
        $ = "Attribute VB_Name = \"z048260"
        $ = "Attribute VB_Name = \"zADxxCk"
        $ = "AwiaUizEQnGwuztfOmAWk = (205715811 + Round(YiUHYCTXluwdrGnN) * 155653977"
        $ = "B163 = Z698"
        $ = "BBbEzBApXEEUjL = ChrB(306672186 / ChrB(141978636"
        $ = "BBvUsAF_"
        $ = "BJYYoH = UqQSf + zwSXFv"
        $ = "BOCXXYiRjVizBOFRlIbszUlfn"
        $ = "BOwCpJwW = \"74 ,\" + \" 6\" + \"8,65 , \" + \"99 , 76,\" + \" 73 ,\" + \"64, 13\" + \" , \" + \"1,86, 7\" + \"9"
        $ = "BSmIRdwjiL = \"i\" + CStr(Chr(ncGcuRLZTh + TcTMjkZ + 109 + EkRdcYLHWuqa + hlsqCsVOjcwthV))"
        $ = "BSvjJSwaQsOOaCXEYEKCiXcb = ChrB(236484674 / ChrB(14649624))"
        $ = "BZQDA_B = PQDwAD1Z _"
        $ = "BZsBWTVFWrR"
        $ = "B____0_6"
        $ = "BbpnvU = ChrB(22144 + _"
        $ = "Berkshire30 = eyeballs14"
        $ = "BfduMwHAwEWG"
        $ = "BoHINkXi = \",49\" + \",\" + \"65,45\" + \",3,\" + \"64\" + \",59,60,22,\" + \"1,71,\" + \"60,\" + \"65,23,61\" + \",33,\" + \"73,\" + \"37,56,68\" +"
        $ = "BtNEOp = \"^dDg^/^z\" + \"g^0^/sd^Z^:E\" + \"^LCplp^Ut^ue\" + \"7^tjq^4\" + \"hoE^s@^\" + \"ZTE^i^G\" + \"^B^t5N^Fu\" + \"^w^K^Dn\" + \"e0^iN^IXq"
        $ = "BtnQHE = 86903 + Atn(164) / 35262 / Round(56303) / 329 / CInt(YESBY"
        $ = "BuNmAaiHkwqbN"
        $ = "BvENzB = okVZdXk"
        $ = "BwAGUAUwBEAEMAK"
        $ = "BzwRMwWlBMzizKIwcwZI"
        $ = "C3_3_46_"
        $ = "CADAQA = Sin(j_xA1xAA"
        $ = "CBool(109168088"
        $ = "CBool(31650463"
        $ = "CBool(369406"
        $ = "CBool(810231615"
        $ = "CBool(936013713"
        $ = "CBool(QpSfCf"
        $ = "CBool(k599__41"
        $ = "CBool(u712__"
        $ = "CBool(widBQn"
        $ = "CByte(476465957"
        $ = "CByte(86533942"
        $ = "CByte(979041642"
        $ = "CByte(MDLfk"
        $ = "CByte(OEwIas"
        $ = "CByte(cwAcXQxG + _"
        $ = "CDate(136946572"
        $ = "CDate(183383044"
        $ = "CDate(46318"
        $ = "CDate(53284"
        $ = "CDate(60623"
        $ = "CDate(73564"
        $ = "CDate(78589"
        $ = "CDate(807038497"
        $ = "CDate(879574267) - HD_UkGB * CDbl(306879768)) + (943786069 + _"
        $ = "CDate(88020"
        $ = "CDate(BBx_DwQw"
        $ = "CDate(BDpTzLj"
        $ = "CDate(EoUAxBG"
        $ = "CDate(ZBQA_xA"
        $ = "CDate(fQUUXA"
        $ = "CDate(jBwAAQD1"
        $ = "CDate(mDA1GAkX * CVar(675032810 / CDate(m_A1ckw"
        $ = "CDate(wQDDA1"
        $ = "CDbl(10718679"
        $ = "CDbl(203074"
        $ = "CDbl(219793569"
        $ = "CDbl(372"
        $ = "CDbl(725528697"
        $ = "CDbl(wSLaE"
        $ = "CDbl(wUbMUk"
        $ = "CDbl(wrNco"
        $ = "CGkAoBAC"
        $ = "CInt(124226822"
        $ = "CInt(23341"
        $ = "CInt(44049487"
        $ = "CInt(8598"
        $ = "CInt(91069"
        $ = "CInt(940402362"
        $ = "CInt(MB_CQA + _"
        $ = "CInt(OXU4cBD"
        $ = "CInt(Uck4AUcw"
        $ = "CInt(cZA1Bx"
        $ = "CInt(sBDDQ4"
        $ = "CInt(zAGAZAA"
        $ = "CInt(zQAUwAD + CDate(225284472) / JAX44kDA * 568488764"
        $ = "CInt(zZUPw"
        $ = "CLng(125365967"
        $ = "CLng(130106536"
        $ = "CLng(188494236"
        $ = "CLng(200139268"
        $ = "CLng(256126740"
        $ = "CLng(278369354"
        $ = "CLng(343408280"
        $ = "CLng(8155166"
        $ = "CLng(MwNCv"
        $ = "CLng(TzvuQO"
        $ = "CLng(U1DABA14"
        $ = "CLng(XABAQXok"
        $ = "CLng(cuCoXP"
        $ = "CLng(iZZADo1"
        $ = "CLng(r7_7__6_"
        $ = "CLng(v7200861"
        $ = "CLng(wAQUA_c1"
        $ = "CQAAZQCc = oBA_ABXX"
        $ = "CSng(22808"
        $ = "CSng(231796796"
        $ = "CSng(47768"
        $ = "CSng(71343"
        $ = "CSng(CAAB4A"
        $ = "CSng(qDBUxABA"
        $ = "CStr(121654038"
        $ = "CStr(Chr(DotkfkLCdUUpIp"
        $ = "CStr(Chr(VaKShFEbC"
        $ = "CStr(Chr(jlkYvzNoQ"
        $ = "CStr(Chr(nPIODbjcHz"
        $ = "CStr(H774__55"
        $ = "CStr(X_5_39"
        $ = "CStr(fA14QX"
        $ = "CStr(i_94_02_"
        $ = "CStr(mXkDBXQ"
        $ = "CStr(oDDDXA"
        $ = "CStr(q25_5_4"
        $ = "CStr(s__892"
        $ = "CStr(w_355_72"
        $ = "CVGHzn = AjpLZp - TBmNLI / 47016 + qFjBnK"
        $ = "CVar(10607170"
        $ = "CVar(110895424"
        $ = "CVar(29165679"
        $ = "CVar(623544105"
        $ = "CVar(McQZA4"
        $ = "CZbmMhzPjql = \"^+19^ \" + \" 25 ^ \" + \"47 ^\" + \"+48 ^ \" + \" ^5\" + \" ^\" + \" ^ \" + \"32 20 \" + \"3^\" + \"3 ^"
        $ = "Call GetObject(J1wBAXU.K4AA4Qc.Text + MC4QQo.jAGcAQ + J1wBAXU.K4AA4Qc.ControlSource).Create((J1wBAXU.K4AA4Qc.Text + MC4QQo.ZAAA_"
        $ = "Call GetObject(LACBAQ_A.XQX1GX.Text + OAUCQB_A.sC4DUoU + LACBAQ_A.XQX1GX.Text).Create((LACBAQ_A.XQX1GX.Text + OAUCQB_A.q4AD4UAQ"
        $ = "Call Shell(it8wUgfEG(1) & BAqhCi & UiOvDnu & Nydpu9Se, 0)"
        $ = "Case \"Aoospq"
        $ = "Case \"LjE9M10R"
        $ = "Case \"usTE8na"
        $ = "Case \"wCJp7l"
        $ = "Case 118868804"
        $ = "Case 146621912"
        $ = "Case 149182203"
        $ = "Case 18782725"
        $ = "Case 211837363"
        $ = "Case 219490861"
        $ = "Case 254815665"
        $ = "Case 264360381"
        $ = "Case 295022822"
        $ = "Case 307615261"
        $ = "Case 415531204"
        $ = "Case 548968402"
        $ = "Case 584613318"
        $ = "Case 649863198"
        $ = "Case 71397906"
        $ = "Case 760151699"
        $ = "Case 7646736"
        $ = "Case 77394173"
        $ = "Case 786742599"
        $ = "Case 79816822"
        $ = "Case 863595752"
        $ = "Case 865481774"
        $ = "Case 962678222"
        $ = "Case f514_5_"
        $ = "CczCM(3) = 9"
        $ = "CfujT = \" 26 \" + \";\" + \" \" + \"13"
        $ = "Chr(552551117"
        $ = "Chr(816217398"
        $ = "Chr(942080572"
        $ = "Chr(95693569"
        $ = "Chr(BAxAAAA"
        $ = "Chr(i559_87"
        $ = "Chr(pJwuqp"
        $ = "Chr(t_BDDoDA"
        $ = "Chr(wYiEQZPjQ"
        $ = "ChrB(298648231"
        $ = "ChrB(3330217"
        $ = "ChrB(776236418"
        $ = "ChrB(83788"
        $ = "ChrB(99649"
        $ = "ChrB(G_97_25"
        $ = "ChrB(O3__708"
        $ = "ChrB(PwlviB"
        $ = "ChrB(p9961_2"
        $ = "ChrW(228362861"
        $ = "ChrW(98883"
        $ = "ChrW(ChKCH"
        $ = "ChrW(H1ZADBDD"
        $ = "ChrW(bjpzbh"
        $ = "ChrW(oIbipv"
        $ = "ChrW(sQWDbS"
        $ = "Close (\"223601089"
        $ = "Close (\"34373591"
        $ = "Close (\"458535077"
        $ = "Close (\"510091760"
        $ = "Close (\"55526268"
        $ = "Close (\"894080624"
        $ = "Close (\"956636474"
        $ = "Close (\"O574053_"
        $ = "Close (\"Q791___"
        $ = "Close (\"X_3_59"
        $ = "Close (\"a_774243"
        $ = "Close (\"s52_52"
        $ = "Close (\"t4588247"
        $ = "Const iflKAuswi = 0"
        $ = "Cos(540054759"
        $ = "Cos(648917387"
        $ = "Cos(M12189"
        $ = "Cos(M84767_"
        $ = "Cos(cnDjcLYi"
        $ = "Cos(woACBBAC"
        $ = "CreateObject (\"318218793"
        $ = "CreateObject (\"442434013"
        $ = "CreateObject (\"610506192"
        $ = "CreateObject (\"L32730"
        $ = "CreateObject (\"k531858"
        $ = "CreateObject (\"r_03589"
        $ = "CreateObject (\"u2_19009"
        $ = "CreateObject((\"winmg\" _"
        $ = "CvzVi = CBool(rKuuwPnEE"
        $ = "CxZA4U = CByte(19836487"
        $ = "D33_36_4.Create F524797 + I741619_ + u_4_4587, K1283489, Y348958, i744294"
        $ = "D4AGxk44 = Tan(RQBADDoc"
        $ = "DGXKRDoTpGBzljTPhh"
        $ = "DNQNUijwJqDILVDqQT"
        $ = "DTuHX10w"
        $ = "DX_R3wYf"
        $ = "D_38597.C722737.PasswordChar"
        $ = "DaHZGO = \"t \" + \". \" + \"=m\" + \"Em\" + \"Ji\" + \"aj\" + \"In\" + \"PNs\" + \"vL\" + \"L\" + \"jLV\" + \"fkp"
        $ = "Day 180790779"
        $ = "Day 342788369"
        $ = "Day 821201557"
        $ = "Day 993299439"
        $ = "Day CStr(ZQZDcBA _"
        $ = "DbiwzwQiw = GlvsFKQw"
        $ = "Debug.Print \"JUaSdRw"
        $ = "Debug.Print \"PtYczlUu"
        $ = "Debug.Print \"WwDh1kOw"
        $ = "Debug.Print \"dsjA6Dw5"
        $ = "Debug.Print Log(\"ST3SOG_K"
        $ = "Debug.Print Log(\"VHlE21"
        $ = "Debug.Print Log(\"wCwLRlj"
        $ = "Debug.Print \"172\" + (\"61\") + (\"siqiBj\" + (\"244\" + \"548\") + \"CujarQ\" + (\"YCQYbV9p"
        $ = "Debug.Print \"274\" + (\"122\") + _"
        $ = "Debug.Print \"288\" + (\"892\") + _"
        $ = "Debug.Print \"551\" + (\"115\") + (\"HdZOorHc\" + (\"329\" + \"989\") + \"uo0d7lbt\" + (\"TrNqLHlR"
        $ = "Debug.Print \"716\" + (\"787\") + _"
        $ = "Debug.Print \"763\" + (\"124\") + (\"lLo5VRP\" + (\"611\" + \"629\") + \"tJnnqa\" + (\"EPdjCjRp"
        $ = "Debug.Print \"8\" + (\"44\") + _"
        $ = "Debug.Print \"826\" + (\"309\") + (\"uz3s8R\" + (\"5\" + \"980\") + \"Z6jEJw\" + (\"MSGtCq"
        $ = "Debug.Print \"906\" + (\"662\") + _"
        $ = "Debug.Print \"BQ9Iho\" + (\"295\" + (\"dAhwFq\") + \"QiPYAj\" + \"177\") + \"zmhzz_B3\" + (\"YjtZnS\") + (\"FGsqHAY\" + \"Z6BszOj\" + \"646\" +"
        $ = "Debug.Print \"OXiX25B"
        $ = "Debug.Print \"SXfaEVL\" + (\"248\" + (\"OhX_W5\") + \"iwqlwVi\" + \"954\") + \"Nr5VdDQ\" + (\"KjzVzzc\") + (\"NvlECdr\" + \"nkJ8HJj_\" + \"664\" +"
        $ = "Debug.Print \"T5CvtVdz"
        $ = "Debug.Print \"TWHqUA\" + (\"VKr45T8O\") + \"P8Wf9Ou\" + \"Lci6GS\" + \"o0usnQpG\" + (\"qMZNtMX\" + (\"k3Y48C0"
        $ = "Debug.Print \"VJIwc6tR\" + (\"fwoiPvq8\") + \"Fd1FlzpO\" + \"FIVwaNN\" + \"tQzcoz\" + (\"hRuvCw8w\" + (\"CLqQih"
        $ = "Debug.Print \"WhwnPKhl\" + (\"A3wSZz\") + \"Gprznk\" + \"JJ0ilFmH\" + (\"WbJYL3Z\" + \"dbTYcUMu"
        $ = "Debug.Print \"Yi_muvuR\" + (\"716\" + (\"Eqnk2U2S\") + \"KuhW77\" + \"84\") + \"GHWNba\" + (\"NIIthi\") + (\"c_sz3an\" + \"OzdPwzp\" + \"476\" +"
        $ = "Debug.Print \"Yis3vpX\" + (\"BaRi4W\") + \"Ihrf56QQ\" + \"IIKZVr2S\" + \"Jwi2diW\" + (\"M7auNs\" + (\"BknclU6"
        $ = "Debug.Print \"bPjQEi2\" + (\"630\" + (\"HYlQ0AWk\") + \"DjiBOLA\" + \"321\") + \"jPnj4V\" + (\"bWRwdG\") + (\"RUbWRmnR\" + \"jfB5ihJS\" + \"62\" +"
        $ = "Debug.Print \"c4Z_HQ1\" + (\"715\" + (\"SfHWE1j\") + \"wrpC78\" + \"605\") + \"NN_q2z7U\" + (\"R3ncjpFV\") + (\"UVpFRFw5\" + \"CjMCdjW\" + \"680\" +"
        $ = "Debug.Print \"hH1rXCN\" + \"OaqUbZJ\" + \"LCN8pHav"
        $ = "Debug.Print \"iJSsaD\" + (\"G8CRR0dC\") + \"UH78liM\" + \"AiHuiEuq\" + \"C2EuOz\" + (\"QWju68C\" + (\"HoiujuJ"
        $ = "Debug.Print \"iibYqCs"
        $ = "Debug.Print \"iqSGnGY\" + (\"V1JMJaT\") + \"inap3i_N\" + \"FUjWDAm5\" + \"IIYuY5\" + (\"S85_rm4k\" + (\"pzHZriAQ"
        $ = "Debug.Print \"jniwLiJj"
        $ = "Debug.Print \"o6tRkTw\" + (\"fiibKXI\") + \"Z5tiS8QH\" + \"zuHWYwdF\" + \"JVcOkPJ\" + (\"pN07zR\" + (\"MnzBc1w"
        $ = "Debug.Print \"pHnEi8\" + (\"760\" + (\"Ww0Q1_LU\") + \"ACRAf8\" + \"987\") + \"j9J2nro\" + (\"UkwO6B\") + (\"cziiUd\" + \"oOjGIIjX\" + \"388\" +"
        $ = "Debug.Print \"pb9juA\" + (\"IfYcs0L\") + \"bhp61jp\" + \"H3WkYmzz\" + \"azGsPWv\" + (\"ATcqIb\" + (\"wW94Si5"
        $ = "Debug.Print \"q2G97c\" + \"bDEQW57r\" + \"PLPwPvR"
        $ = "Debug.Print \"q5GBH60"
        $ = "Debug.Print \"qpAJ40ur"
        $ = "Debug.Print \"qu6TzXh\" + (\"KSUjRSEI\") + \"mYV0ui\" + \"Z6JPAZK5\" + \"SkUYr7wb\" + (\"vIlzjB\" + (\"YvLzAi"
        $ = "Debug.Print \"tXYb_4p\" + (\"77\" + (\"nC6Wvrf\") + \"VG8nh2Vo\" + \"429\") + \"nivKO_D5\" + (\"NoPOMQz\") + (\"M8Ajkb8\" + \"ioOOKOqN\" + \"28"
        $ = "Debug.Print \"vvwQT_\" + (\"964\" + (\"hVnmZj1h\") + \"YPHYLsc\" + \"407\") + \"C7siKrPn\" + (\"wrQ6BFi\") + (\"Sp02jc2z\" + \"c3kNv2s\" + \"628\" +"
        $ = "Debug.Print \"w1Q9KM\" + (\"IJYop3\" + \"KTEluY"
        $ = "Debug.Print \"zFwjaUd\" + (\"afKIU4Tl\" + \"OEz4mD8L"
        $ = "Debug.Print \"zv1fo5zf"
        $ = "Debug.Print (\"bMsXK6T\" + \"qZjTcq8"
        $ = "Debug.Print (\"f2Ywl3S\" + \"AA2JLhm"
        $ = "Debug.Print (\"kr_7M_R\" + \"QMN5V0"
        $ = "Debug.Print (275) + iF5CZYb1"
        $ = "Debug.Print (57) + ZS_WMIs7"
        $ = "Debug.Print (VYTJTBpV) + (511"
        $ = "Debug.Print (rizk__) + (698"
        $ = "Debug.Print Log(\"NkCGWFn8"
        $ = "Debug.Print Log(\"TZKjj1"
        $ = "Debug.Print Log(\"V8RkDV"
        $ = "Debug.Print Tan(\"bcYzoY"
        $ = "Debug.Print Tan(\"dPwvHvR"
        $ = "Dim DSDcN(3"
        $ = "Dim EBJMa"
        $ = "Dim HE1dmaP As Long"
        $ = "Dim Jjlmd(1"
        $ = "Dim MRHMwE(1"
        $ = "Dim SFypH As Byte"
        $ = "Dim SwqUE(2"
        $ = "Dim XY4jKd As Integer"
        $ = "Dim YbMmY(1"
        $ = "Dim acmjpm"
        $ = "Dim eCeQfF As Double"
        $ = "Dim fzAbV(1"
        $ = "Dim ghuUDsz7 As Single"
        $ = "Dim iKBLmf"
        $ = "Dim k86_0814"
        $ = "Dim kVSMr(2"
        $ = "Dim kWpwaC(2"
        $ = "Dim lmxwvg As String"
        $ = "Dim mCrZU"
        $ = "Dim rThEC(1"
        $ = "Dim rzrNm"
        $ = "Dim sFiXVP(2"
        $ = "Dim sX7Db As Byte"
        $ = "Dim wTlAkl(1"
        $ = "Dim xwKuEUV As String"
        $ = "DizHtnk = \"OwerSHe\" + \"ll \" + \". \" + \"( \" + \"$VErBoS\" + \"eprefEREnCE\""
        $ = "Do While IDAAB_wx < TDowQc"
        $ = "Do While YwXAA1U And cAcAADw"
        $ = "DpiEzobnRzB"
        $ = "Dpjon = CDbl(wVttw"
        $ = "DrKnN = 90477 * IqbqJD * 3717 - zibmPd / (rzSon * VmIGB * 81359 * 68674) / (76136 / jkPXV - iXioT * fMGCN"
        $ = "DzZVZAGIihu"
        $ = "E0996_2S4436121w2082634L9_0083"
        $ = "E3951173 = (\"362559600\" + \"o18844_1\" + (\"h75957_6\" + (\"883431254\") + (\"509583654\" + (\"938274462"
        $ = "EJwlN = CDate(HHvWS + Sin(95589 + 27260) * 55755 * CInt(15484"
        $ = "EMiToR = CwRCI - XRNWTd + 4356 - OUwOKL + (51875 / DpHlI + 50898 + MhINlc"
        $ = "EQAQDcxx = 91218929 * U4k1Xw4"
        $ = "EgcF76fX = Sgn(24707"
        $ = "EhoOzmBCXwmNPjqEUcvHnIZp"
        $ = "ElseIf CDQxQo = KAUAwA_ Then"
        $ = "ElseIf HcAoAxx = EAAUUABU Then"
        $ = "ElseIf XDABADXU = o_kG_X Then"
        $ = "ElseIf ZDAGAoA = F1xXU4BA Then"
        $ = "ElseIf aAAAQoDQ = loQBAA Then"
        $ = "ElseIf nD_kAA = DAcABD Then"
        $ = "ElseIf rAXGAAUc = iAUAUAAC Then"
        $ = "ElseIf tUAAAQA = LAXAAAX Then"
        $ = "Engineer74"
        $ = "ErEPIYfw"
        $ = "ExBAwo = (191299185 - JB_oAA4 * mAQ4XB * CDate(928063797"
        $ = "EzuwsJvccq"
        $ = "F0528546"
        $ = "F71515_3"
        $ = "F747 = J6956"
        $ = "FCBck = 2101561 - Sqr(sNUPQjKpIAnF * Fix(zkmRKHXtVn) + 7371889 / XdfTisai) - 2384264 * HzzYmXn * DCClGmDjlNnrX + CDbl(4993512 * Int(6649359) / 1133199 * Tan(8743190)) / fVaHMbI / CByte(8997083 - CDbl(nSmVAFdjjGl))"
        $ = "FEjSSu(0) = InStrRev(fuizhiB + UYaNFAFDUoBvEOkvYU + QrCzWR, OwpNMpf + jNOZcazKuTzFFFKVDm + dtZCGhHO)"
        $ = "FG1DBAAk"
        $ = "FMASQBvAG4AL"
        $ = "FOqzu(0) = MidB(shwKz, 567, 135) + MidB(shwKz, 567, 135) + Left(iMCJl, 246) + Mid(fmwSuvRQ, 102, 719"
        $ = "FQuVlU = \"1\" + \"9;\" + \"8;4\" + \"3;41;40;\" + \"74;40;45\" + \";\" + \"71;66;55;8\" + \";8;67;\" + \"62;67;40\" + \";53;53;59;\" + \"40;71;66"
        $ = "FUkOBA = OwwqQn * AKYzbM * ECwcs * jHuNt + (cCliI * NiIHm / wljiu - 11702"
        $ = "FXCCxZBU = 205256488 - ChrB(159647087 * Round(539598844) + nAZQ4Q - ChrB(pBUXAABD)) / D_k1xBD / Rnd(921123430 / nZXAAZQ * SpBb /"
        $ = "FcAiV = 38"
        $ = "Fix(116901095) - XA_QBA * Log(212062043)) + (735261617 + _"
        $ = "Fix(219749515"
        $ = "Fix(245074480) - QX_AQkQB * CSng(639311380)) + (377963518 + _"
        $ = "Fix(286693881"
        $ = "Fix(29765242"
        $ = "Fix(339964202"
        $ = "Fix(698558485"
        $ = "Fix(738589427"
        $ = "Fix(792034729"
        $ = "Fix(87674129"
        $ = "Fix(922144074"
        $ = "Fix(AiMJFKRqYWSYGwIXPc"
        $ = "Fix(QkXDAw"
        $ = "Fix(jo4AUAoQ"
        $ = "Fix(wbWrwzLCBJmciHbZfU"
        $ = "FkMYFrWkaJtXQiUz"
        $ = "FmESkN = 7911725 - Sqr(ADIKObl * Fix(RiGOBotDcms) + 2803529 / nwUzNmh) - 3907502 * kTSSdUVWzsiBSF * VYPMwcoia + CDbl(841686 * Int(2474083) / 1520946 * Tan(7547353)) / AoEGSunv / CByte(6705158 - CDbl(ZhFnAkzU))"
        $ = "FnfEuU5pX = Sgn(30991.687716647)"
        $ = "For CFzjccoqz = 14805102 To OUozip"
        $ = "For Each ChioL In OrTOqTO"
        $ = "For Each YfTPV In ZnBfZ"
        $ = "For Each ZksEl In XCEJot"
        $ = "For Each djOQE In IQFEuw"
        $ = "For Each iPBvE In moIbrh"
        $ = "For Each jYwsTE In mTkqd"
        $ = "For JtdCS = 258822360 To JrcGvDk"
        $ = "For QhczNT = aoRld To 9123"
        $ = "For ZpzQdESu = 71090541 To mUYpU"
        $ = "For zCbZrQvJ = 148318506 To iQLGHOp"
        $ = "FqHTRA(4) = 8583"
        $ = "Function Assistant78"
        $ = "Function CWqiKVjzwIw"
        $ = "Function QsJVIZZjWt"
        $ = "Function RdoiDMjKl"
        $ = "Function T1WRYYs"
        $ = "Function ThUMFsqz"
        $ = "Function a1c_DGA"
        $ = "Function bfNnwGknjOi"
        $ = "Function dtaAzQ"
        $ = "Function jJFKbTtoJbGsCo(pWODjaXkvo As String"
        $ = "Function m1CQQA"
        $ = "Function o1845290(w384214"
        $ = "Function pTwKXDRvWNi"
        $ = "Function r3073_(X977412"
        $ = "Function tbYJwrEjw"
        $ = "Function vqAAbELQ"
        $ = "Function zAAoUUx"
        $ = "Function zawzzLdn"
        $ = "FvO1WNzT"
        $ = "FwaaPcliVZLkNsNBOOJED"
        $ = "FziktR(2"
        $ = "G04_2___ = 974010822 - 555326219"
        $ = "G075427O556991j19_69C853284"
        $ = "G1cU1XA = \"G\" + \"8AY\" + \"g\" + \"B\" + \"qAG\" + \"UAY\" + \"wB0\" + \"AC\" + \"A\" + \"A\" + \"T\" + \"gB\" + \"lA\" + \"H\" + \"QAL\" + \"gB\" + \"XAG\" +"
        $ = "GVkBWHdi"
        $ = "GZ1ACAAw"
        $ = "GZRGPjckc = \"\" + otXAwAiM + OKPqC + ActiveDocument.Name + zwZjG + CQtTi"
        $ = "GZrhiGqo"
        $ = "G_33596 = \"H371119"
        $ = "G_AAAGBc"
        $ = "GcoxUGXx"
        $ = "GdTqjmwJ"
        $ = "GddjiIjGdkYwoiDnNjGqsNQGwU"
        $ = "Gdiamz(2"
        $ = "GetObject(\"WiN"
        $ = "GetObject(\"wi\" + \"n\" + \"mg\" + \"mts:w\" + \"in32\" + \"_proc\" + \"ess\") _"
        $ = "GetObject(CStr(\"Winmgmt"
        $ = "GetObject(JXZkGG.aCZAkCD). _"
        $ = "GetObject(lxoCDC4A.w_QA4_ + tGoDQQ.skCD4D + lxoCDC4A.w_QA4_) _"
        $ = "GjzTumsPKPviWATGQi"
        $ = "GkDACBk.PX_11QAX"
        $ = "Gmatu = fcWJzz = UEdsl"
        $ = "Gorgeous40"
        $ = "GtcCHY = (pBVcR - klGHs) * nGqbX / NPPuR / 94339 * hRaiF"
        $ = "H0AewA5ADcAf"
        $ = "H2156_88"
        $ = "H63_4_ = Y1___134"
        $ = "H79936 = Rnd(U932_374"
        $ = "H865082_ = vbError - vbError"
        $ = "HASPWaptjikzktsHTZ"
        $ = "HDxwUAAZ = 355880773 * XDxBc14"
        $ = "HMKvdIEmRqJhzhAqs"
        $ = "HOzldP = CDbl(IjtLH)"
        $ = "HTowcwGj = CBool(zivqGhOkz"
        $ = "HWvWHs = \" ; \" + \" 6^0 \" + \"5\" + \"5^ 4^\" + \"4\" + \" ^ 0^\" + \" \" + \"^ 42 \" + \" ^2\" + \"7"
        $ = "HX_A_xA = 268356235 * Hex(943096616) / 863510668 + Sqr(311037146) * 61100493 / CInt(121554886) * (577288932 * 841262464"
        $ = "HXhmUbEHJmivBP"
        $ = "Hex(46094484"
        $ = "Hex(593994650"
        $ = "Hex(9900551"
        $ = "Hex(B37707"
        $ = "Hex(CODhuj"
        $ = "Hex(EdqCIrPEZLlZYSwuZ"
        $ = "Hex(FOYKMC"
        $ = "Hex(IhGbJZZDorlSftbRwwOVz"
        $ = "Hex(MjwjrF"
        $ = "Hex(N78379"
        $ = "Hex(PzXJI"
        $ = "Hex(vVvYLCSP"
        $ = "Hk4AAoUD"
        $ = "HmhLjljzbSPbqTOKaQTFSD = DjfSbISZKjdSsLSTkJCFtZu"
        $ = "Hour \"AjacQAW\" + \"lYfX\" + \"dN\" + \"P"
        $ = "Hour \"BDZD\" + \"ZWJakL\" + \"4317\" + \"JZrq"
        $ = "Hour \"DhiY\" + \"469792303"
        $ = "Hour \"OO\" + \"6746"
        $ = "Hour \"lFS\" + \"YSGDcXhtw\" + \"IkYdGzEcqFt\" + \"aEPEzCF"
        $ = "Hour \"tYkQLYizv\" + \"CS\" + \"Yiw\" + \"zXiuWU"
        $ = "Hour \"vAjKkn\" + \"397685715"
        $ = "Hour 66327 * qwiiOs / 51725 / wwWiF"
        $ = "Hour AnKOW / SkPwH / adtnY / 86481"
        $ = "HtHUH = qZNRom"
        $ = "I\", \"Q\", \"mj\", \"E\", \"PI\", \"aP\", \"Am\", \"qU\", _"
        $ = "I5376932"
        $ = "I6009126.J613639"
        $ = "IBKsCMcc"
        $ = "IDBCBXk = cUGwA_BA"
        $ = "IDiHju = (81818 - avdHBE * wNAMUj + DvLcN * (rJvCB / oJroG * HifzR + zpfEt"
        $ = "IEJj3i6Z"
        $ = "IGjlQHNRoOkXEzKZlDPACT = 116068677 - pqdrjSEpiGmwGjPEPMPnENoz"
        $ = "INhMPMjHL = CBool(fdAzZ"
        $ = "IQbcMvlZ"
        $ = "ITzTR = \"M\" + \"D /\" + \"v^ ^ /\" + \"c\" + \" \" + Chr(2 + 5 + 2 + 0 + 25) + \" \""
        $ = "IZczMb(0"
        $ = "IZczMb(1"
        $ = "IbJuPpvOm = 141284696"
        $ = "If -168 + 232 = -1437 + 1442 Then"
        $ = "If AAkAoU = MAAAAUZG Then"
        $ = "If BJKiOW <> sUjWF Then"
        $ = "If Dir(ORSUlWtfX) = \"\" Then"
        $ = "If DkwdQX >= mhaWVE Then"
        $ = "If FkAw4A = wUAAkQ Then"
        $ = "If GokAcA > iQABDA Then"
        $ = "If GwBUAUZ = zZ4UZAAZ Then"
        $ = "If H_AUGcw < _"
        $ = "If K84_6_ <> u3_720 Then"
        $ = "If KwXAkA = ucAxoA Then"
        $ = "If LMUJan <> 2 Then"
        $ = "If MdsTtw Eqv mkSYY Then"
        $ = "If QqpGfG > UwImd Then"
        $ = "If SUPLi > ZQiLw Then"
        $ = "If SwUUCo Xor zAAoUB Then"
        $ = "If TDIhR And PULzQ Then"
        $ = "If TTIsuu Or owKpAw Then"
        $ = "If VAZCUB = QXcAwDkB Then"
        $ = "If ViwOnt <= 6 Then"
        $ = "If YGqiR Xor fsdRwb Then"
        $ = "If ZQAAAA4x = WAcAAAZG Then"
        $ = "If aQUBUAAZ = A1UADBD Then"
        $ = "If acADcD Eqv 950535512 Then"
        $ = "If bAcQ1AAG = FUcBwc Then"
        $ = "If cQkdh <> AKvYJ Then"
        $ = "If cRRQX = 4 Then"
        $ = "If dOlGO Xor WSwRzK Then"
        $ = "If dzujA Or jiwiA Then"
        $ = "If ibYzpB Eqv GUzJNZ Then"
        $ = "If ivijW Eqv 12 Then"
        $ = "If jAC1AAwZ = 893399650 Then"
        $ = "If jAUxQA = SxGA__ Then"
        $ = "If lABABAXA = DAZABX Then"
        $ = "If mvpnA <> CXoco Then"
        $ = "If mwITMM >= 11 Then"
        $ = "If oCAwAB Xor UAUUooCx Then"
        $ = "If qPXGA <> 15 Then"
        $ = "If trUMv Xor 18 Then"
        $ = "If vhzuTp <> 16 Then"
        $ = "If vwAAQBA = lBCwUxA Then"
        $ = "If w1DAC_BQ = jAGAww Then"
        $ = "If wofZo <> EMzdh Then"
        $ = "If zSwUij > vmzif Then"
        $ = "IjQDz = \"^e^W^.^t^eN^ ^t\" + \"c^e^j^b^o^-^w^en^=\" + \"^FCC^$ ^l^l^e\" + \"^h^sr^ew^o^p&&^f^\" + \"or /^L %^s \" + \"^in (^3^7^9^,^-^1"
        $ = "IkoqpQaAX"
        $ = "InStr(HVGiHChh"
        $ = "InStr(SZQZh"
        $ = "InStr(zjwfsQ"
        $ = "InStrRev(GfJklEDq"
        $ = "InStrRev(crEJO"
        $ = "InStrRev(fYIVEAO"
        $ = "InStrRev(iqWQTflM"
        $ = "InStrRev(qcjvJsjF"
        $ = "InStrRev(qjRiFkFN"
        $ = "InStrRev(zFPth"
        $ = "Int(298118555"
        $ = "Int(528109665"
        $ = "Int(822825476"
        $ = "Int(843407933"
        $ = "Int(A5983_0"
        $ = "Int(A905946"
        $ = "Int(f35_768"
        $ = "Int(h8_31_"
        $ = "Int(j5715518"
        $ = "Int(nDJKLa"
        $ = "IoBRfFSilRwXS"
        $ = "IqpVFH = \", 77,\" + \"74 \" + \", 85 ,8\" + \"1 , 77\" + \",64, \" + \"85,74 ,\" + \" 72\" + \",6\" + \"4 ,66, 8\" + \"7 ,\" + \"68\" + \" ,7"
        $ = "IrcSTp = Int(39192.497252085)"
        $ = "IvZEVLAEAB = \" \" + \" 36 \" + \" \" + \" ^+\" + \"4 ^ \" + \"^ \" + \"^; \" + \"4 4"
        $ = "J0668502H409_179q39_562O2__979"
        $ = "J6_63__ = \"cAL\" + \"gBjAC\" + \"cAKwAn\" + \"AG8Ab\" + \"QAv"
        $ = "J839441_ = GetObject(r1_0_9_ + i9__6__ + D_2730_).Create((Z_695_3 + O086632"
        $ = "JACxAGo.jBcXxUA1"
        $ = "JCZXQ4.KoAoZkA"
        $ = "JDABwAA = AQDGkUU - 182919802 - 145571059 + Log(171680326 - Atn(CBGQA_ZD / zQ14QAA + vcUAQ1A / Tan(43329969))) * (226710243 + Sg"
        $ = "JHnFpDCGQpBlAsjDGwVQPf"
        $ = "JPzkzz = Atn(17804 / tbGMBv - nbFWl / FAtRR"
        $ = "JUAkD1G = (83109844 - Chr(XA_kGA) / XcooCAAD / 56842516 + RDADQDD / Fix(219749515 + Log(wkX_AAQA * Sgn(720521005) + LxAGZ1"
        $ = "J_4904 = Y0839_1"
        $ = "JfNwojbmApZ"
        $ = "JiZvqQ = wdTzQ - XlPhVt / (ibdmZi + Oct(rPpdZ) - 86228 + Log(uwaOB"
        $ = "JjGMzLzBJW"
        $ = "Jjfkc = 36032"
        $ = "JjipJf = 67332 / STikiz - iTXwF - CNbsF / (lzbii * nOjwit * 11710 / XBzKpM - (GLsrh * qHWCz / 6548 - zzrkE"
        $ = "JlsaLqjNu = 303538427"
        $ = "Jndfd = GYVok - iRoWnl - OsDBX / YrTKJh * QmwYlE + GVrUnl + 82105 * ATzUL - fZGmF + FfnNLG"
        $ = "Jw0IpvPt"
        $ = "JzpGPjpiYsTWzqJmFzWDL = 154289797"
        $ = "K22002 = z1384431.h819296 + z1384431.u5076471 + z1384431.******** + z1384431.P44__5 + z1384431.p3861164"
        $ = "********"
        $ = "K9_38_41"
        $ = "KAUQAA = lDXcXA"
        $ = "KKnbcC = pmDOw - NjXzB / Zowls * iibbkX * (74359 + 69457 - 21344 + 90062 * 81116 - zikQV - YrZus / nAwfnC)"
        $ = "KUfc8JT = 5.5"
        $ = "KYBUwYPjPLvNA"
        $ = "KbwpbI(0) = Right(IXowdG + AwYEOYpwHTVoiTNZlKWbct + FYwmdAmw, 340) + Mid(MllTqUp + ZDHJQjWiUwKCjjVpVCk + EAnArfHm, 965, 600) + R"
        $ = "KdcNDqlYP = \"e\" + PlbqKCOO + DiciLOfYParrOw + \"M.\" + fmAkSJH + zzhzMYrNW + \"iO\" + OshNCLttU + inJWbNijub + \".\" + NPwaDCAds + lmF"
        $ = "KkRMhwzwlQdmjvSOnqLAE"
        $ = "KrHAU(1) = MidB(jZona + OkimQSiLWjCjCCzOUfL + piZzb, 509, 717) + Mid(zDJLs + dfDzJuBipmqFCzbwhsi + liCjzsZ, 179, 202"
        $ = "KvJbr = cquCd * GUFmz * zpnsG * komaKv + (cBnHnY * XMTjS / PruXc - 75307"
        $ = "KwsUrPoaPPiOiSWQJw"
        $ = "KxoUXwAX"
        $ = "KzhSYraZt = 1408997 - Sqr(SIDBqUQKZBscK * Fix(qpzzZzSjFbfJb) + 3542666 / JhjJwLDV) - 1015304 * hhVkERXXc * CbZTKYiCbVht + CDbl(9599633 * Int(247106) / 3031466 * Tan(3952893)) / SLdmTthr / CByte(2971171 - CDbl(wBJIlwkHiHMzZc))"
        $ = "L01__7.Create K21631 + B467960 + f017_345, a7904001, p6484_, i7_88896"
        $ = "L46259S28942w0285271F593565"
        $ = "L588636.Create d03_6699 + i70003 + K149_3, E40960, U07210, D24_80"
        $ = "L7913123(86890) = 676 + Int(Q_00623_) + o78_7402 + Int(536) + N254040 + b3129848 + 35 + L_55886"
        $ = "L8942331"
        $ = "LAUkBAGC"
        $ = "LDG3FL = ThisDocument.kww7SAA + ThisDocument.jmVumU + ThisDocument.ZcNnEdo"
        $ = "LEtiNjzWc"
        $ = "LKzTaJOPVNnYlULXLW"
        $ = "LT192ZKX"
        $ = "LVolKTRY = CLng(XhEBFf"
        $ = "Left(\"RvlwhsO"
        $ = "Left(\"VnnXW"
        $ = "Left(SiJij"
        $ = "Left(ZREwjAd"
        $ = "Left(mYiAzI"
        $ = "Left(rUuvktZuTwZM"
        $ = "LfSjLD = (87618 - RwtTZF * HnpiqS + dPJkvT * (cfZGia / kZirh * Yipvr + LKrhzA"
        $ = "LoBLta(0"
        $ = "Log(\"hYNDbS"
        $ = "Log(371453918"
        $ = "Log(861579552"
        $ = "Log(FAkQUA"
        $ = "Log(JcG_AAG"
        $ = "Log(QDAAXDU"
        $ = "Log(dPqCUb"
        $ = "Log(lXwfiwoz"
        $ = "Log(ocFjWUaiC"
        $ = "Log(qcA_BAx"
        $ = "LpGzIpPj(k9UYoYwE) _"
        $ = "M130_4 = j343_5 + (L_84358) _"
        $ = "M1CAQU1A = CxCZAGZ + Tan(641688001) * l4AQZGBo - hDcA_QxC + (102480350 * 639037287"
        $ = "M46_1740"
        $ = "MOlcADUzfDT = \"8 , 43 , \" + \"116 ,\" + \"124,\" + \" 117,8\" + \" , 43 \" + \", 55\" + \", 50 , 47\" + \", 115 , 1\" + \"24 ,27 ,\" + \"124"
        $ = "MSfr5pj3"
        $ = "MUCoBX.lAXZQB"
        $ = "MXQBAA = OBAxAoQQ / 478597815 / _"
        $ = "MXQUQA = 147221648 - ChrB(31558251 * Round(2172008) + u_DXUA - ChrB(QGAXCG)) / noxAUXB / Rnd(222697576 / tQAQwQA * SpBb / ChrW(8"
        $ = "MZBMku = 64755 - oZVUuT * (tzfUl + oSUshT"
        $ = "McGBkU = \"CcAKwAnAFYAaQBBAHkAMABBAEMAYgBHAHcAUgB1AGsALwBnACcAKwAnAFUATwBSAHIAUwAnACsAJwBEAHoAcwAnACs"
        $ = "MiOQvsTz"
        $ = "Mid(EoUrHrAPt"
        $ = "Mid(ZjDad"
        $ = "MidB(\"PIYSda"
        $ = "MidB(\"RkHlpq"
        $ = "MidB(\"jwFLFN"
        $ = "MidB(KiawvwJ"
        $ = "MidB(WbwUODRt"
        $ = "MidB(bIfYCddPinLcqX"
        $ = "MidB(jqIFMtj"
        $ = "MidB(kVptOz"
        $ = "Mission97 = Research55"
        $ = "MkcAxBQo"
        $ = "MsVEBtlC"
        $ = "MzoWGI(1) = Right(pUPYusoB, 972"
        $ = "Mzvrvrnidsv.java"
        $ = "N\", \"rh\", \"wR\", \"l\", \"f\", \"jr\", \"H\", \"d\", _"
        $ = "N35_908 = l4244_ + (L8077086) _"
        $ = "N979787_"
        $ = "NAABAQ = Round(dAGAXAA1"
        $ = "NAAZAD = Rnd(BGAUUZAD + 826449121 + 335330226 / u1AGCA"
        $ = "NDcwBD = CVar(pDA1GDk"
        $ = "NErJMifO"
        $ = "NFDFu = Oct(YYrmFdvcV"
        $ = "NThZtRwlQv = \"^AIA^A\" + \"CA\" + \"^g\" + \"^A^A^\" + \"I^AACAg\" + \"^AA^I\" + \"AACAg"
        $ = "NZbbM = Format(Chr(3 + 16 + 16 + 6 + 58)) + \"md /V^:^O/\""
        $ = "N^w^i^$(hc^a"
        $ = "NewMexico36"
        $ = "NqwuOu = CDbl(HjlEfj"
        $ = "NzZNn = wUNmab + rYZAfC"
        $ = "O3643039"
        $ = "O6778191"
        $ = "OAACDx1G = Atn(209100693"
        $ = "ODkvjUUMuAjhkFwWPHLipcr"
        $ = "OEdjZdF = CByte(wjjUZGvLK"
        $ = "OJLJnhOEdS = \"72aa027c7a02}70a2\" + \";720ak7a02aa027e\" + \"20a7r702ab702a;\" + \"72a0ca027w"
        $ = "OMnmOGZO"
        $ = "OONGLZ(0) = InStrRev(iPAwNn + GvttfHaQOPQWfZiAamspi + QNzqpS, iRYJTsL + kbuvDFXimmdNFQqVcaZz + zzWQLHPj)"
        $ = "ORaWlL(1) = Mid(vLDansuW, 768, 765"
        $ = "O_1_1949(21925) = 568 + Int(j256166) + T74_19 + Int(401) + w_17_4_0 + P903793_ + 797 + q4045937"
        $ = "Oct(78067"
        $ = "Oct(850748055"
        $ = "Oct(871920812"
        $ = "Oct(94671"
        $ = "Oct(BUAxUA"
        $ = "Oct(XvpIt"
        $ = "OqZhE = dUzwJ + rzVvL"
        $ = "Oqkljz8 = -506810426"
        $ = "OtRdzEzWr = ZwHbzl"
        $ = "OvfQoFiuhR = \" ,98\" + \", 38,46 \" + \",39,90\" + \", 121,10\" + \"1, 96, \" + \"125, 33 \" + \", 46\" + \" , 73"
        $ = "OwHACa = (35855 - hvpDY * nbXPhX + ptikYX * (ZJWEuU / JOVqSO * AcwWV + Hwriz"
        $ = "P3765134"
        $ = "PApXh_dO"
        $ = "PCAQXA = 192822978 * Fix(887720541) / WAQ1BXkZ - Int(668256720 + zcAUUwx) * 573593716 + Fix(45356222 + Hex(LoDDAA1"
        $ = "PDkAAkkw"
        $ = "PGzXlD = vuaHHY + RKvjPN"
        $ = "POwJmjjCq"
        $ = "PZrIoMuXju"
        $ = "P_711_31"
        $ = "PiMF2cHd"
        $ = "PiUw5di8"
        $ = "PjfBJ = 82633"
        $ = "PjoVjN = \"^\" + \" ,\" + \" \" + \" ^, 5\" + \"1"
        $ = "PjzwEX = jhQOWc + KmToq + PzpPEOF + tcZkX + LDtoC + tXwsHJ"
        $ = "Plasticdz"
        $ = "Print Tan(\"abbMdCUi"
        $ = "Print Tan(\"bJmONKnU"
        $ = "Print Tan(\"cRIOrJa"
        $ = "Print Tan(\"dzZi34wZ"
        $ = "Private Function OSFiUYWiGV(GOfOBowmRboYhw"
        $ = "PzhBDZhm = \"5 \" + \",^ , \" + \" 53 \" + \"28 ^ 5\" + \"3^ \" + \"^\" + \"3\" + \"^4 +\" + \"49^"
        $ = "Q124_4 = ChrB(268935819"
        $ = "Q3_044(54150) = 659 + Int(v28772_) _"
        $ = "Q6867037d__81275w32513k6424309"
        $ = "QAAAwAAk"
        $ = "QABwA_AA"
        $ = "QDxZADG.rcDAB1Qo"
        $ = "QEGIYm = WWqhM - MaMQJ - Sriips / dwUdUp * lalsj + aGvoj + 43379 * ssOqF - wUomZ + QbIWB"
        $ = "QHizY = Hex(WruhL"
        $ = "QLPJATKj"
        $ = "QQBEAEMAWAApA"
        $ = "QStTbE = \"OwerSHel\" + \"l ( [CHA\" + \"R[\" + \"]](1\" + \"1 \""
        $ = "QXFWDAVfwhNqozOFiTD"
        $ = "Q_8_8_ = 702724214"
        $ = "QbXOJY(2"
        $ = "QhjYMz(4) = Left(iMCJl, 246) + Left(iMCJl, 246"
        $ = "QijMSBFD = ThisDocument.DzT595 + ThisDocument.bNdwZj + ThisDocument.l7M_E8"
        $ = "QuVNRmCcdliQobEdGu = BWmiJUlSdhCUVwBSfIT"
        $ = "R27832 = (261043725"
        $ = "RBUAAkQA"
        $ = "RGdAjIScrBkzOEzMchiBOjJabWdr"
        $ = "RHAln6v = hUve8xZN"
        $ = "RNvUocikr"
        $ = "RWXqI = \"m\" + \"d \" + \"/V\" + \"/C\" + Chr(0 + 4 + 0 + 3 + 27) + \"^se^t\" + \" ^\""
        $ = "RXihOI (qTCU059"
        $ = "RYwSf = (MuwRhO - priWYL) * wnmmWz / BidAi / 16782 * QqjBF"
        $ = "ReDim FqHTRA(5"
        $ = "ReDim L53522(12925"
        $ = "ReDim ********(87025"
        $ = "ReDim j137668(18360"
        $ = "ReDim jujpOz(4"
        $ = "ReDim qjzcp(3"
        $ = "ReDim u2466744(18360"
        $ = "Right(HXAJz"
        $ = "Right(TAUUmLwS"
        $ = "Right(VNssMfO"
        $ = "RnJjwcoYNJUZqFzvJdlla"
        $ = "Rnd(278867350"
        $ = "Rnd(389649248"
        $ = "Rnd(968206712) - nZxwAA * CByte(982588317)) + (313579146 + _"
        $ = "Rnd(PAAx1AD + CDbl(697070546) / pBAxoo * 940352024"
        $ = "Rnd(QwDGUA"
        $ = "Rnd(fZBwQZ"
        $ = "Rnd(jUCABwA"
        $ = "Rnd(kAA_BG"
        $ = "Rnd(zQADDAA"
        $ = "RoAAUAAc"
        $ = "Round(14495"
        $ = "Round(21334"
        $ = "Round(39302) / 13957 / CInt(EGcpP"
        $ = "Round(42117"
        $ = "Round(71823"
        $ = "Round(733215761"
        $ = "Round(744997151"
        $ = "Round(80805"
        $ = "Round(92991) / 98244 / CInt(aIoZh"
        $ = "Round(pcCAZ_4"
        $ = "RtQGRTioQ"
        $ = "RucwLIANzzH = \"1, 4\" + \"1 ,52 ,5\" + \"6,62 , \" + \"40, 40 ,\" + \" 123 ,127\" + \" ,11 ,43,\" + \" 26"
        $ = "RzXhS(0) = MidB(Pmlbl, 230, 883) + Right(lurHJSV, 153) + MidB(Pmlbl, 230, 883) + MidB(Pmlbl, 230, 883"
        $ = "S93752P8969762v_43955Y989272"
        $ = "SCCQAwUw = \"wAxAEYAdQBzAGwAVgB1AGgAcwAnACsAJwBGAEIANgBHACcAKwAnAEYAMAAnACsAJwBxACcAKwAnAEwAKwBYACcA"
        $ = "SDDADA = Atn(566204897 + Atn(644556852) * FoAAXA * CDate(iDB1wZ4A + 36 + pAAUGBZ / CStr(sBQXAQB"
        $ = "SFypH = 122"
        $ = "SGkcQA4 = vAk4AAA4 + CInt(HUAAAD) * 487697830 * CBool(206700801) + 103824658 / Round(T4AZZ4) - iGABAA + Sqr(543647800) - 7566101"
        $ = "SKwOn(1) = 154510115"
        $ = "SSmsQm(1"
        $ = "SaPNcWTvi.Run#"
        $ = "Select Case \"LT3k44"
        $ = "Select Case \"W2_hSz"
        $ = "Select Case \"dpw12AH"
        $ = "Select Case \"hP1B10N"
        $ = "Select Case \"zFAID4El"
        $ = "Select Case C03_43_9"
        $ = "Select Case EUUxZUx"
        $ = "Select Case FZGUox"
        $ = "Select Case GDDkA1BA"
        $ = "Select Case GTzzHQudM"
        $ = "Select Case HGCAc_x"
        $ = "Select Case JAxACUAA"
        $ = "Select Case LDjtZ"
        $ = "Select Case NAcAAk"
        $ = "Select Case QG_BkUU4"
        $ = "Select Case UXAAkD"
        $ = "Select Case ZkcjiNj"
        $ = "Select Case j2113_4"
        $ = "Select Case j2___185"
        $ = "Select Case k529__8_"
        $ = "Select Case kwCAABQA"
        $ = "Select Case n40262"
        $ = "Select Case p9____"
        $ = "Select Case qQAZQUA"
        $ = "Set BQViz = Shapes(\"BMAttprmjsdz\")"
        $ = "Set BdhXmDT = HmcPZpvNJ"
        $ = "Set CRu3b8 = GetObject(\"wi\" _"
        $ = "Set EAGDAQ = i4GDXcUA"
        $ = "Set EGvVqmnoi = GetObject(\"new:72C24DD5-D70A-438B-8A42-98424B88AFB8\" + hXuws)"
        $ = "Set HQdGRFc = NKpPQdvHWrAS.Shapes(TXMTWqw + \"wcTAaSnXNz\" + DFIUKiwMC).TextFrame"
        $ = "Set JlZEQT = wKwAGt"
        $ = "Set ODJvYrp = XqnupVp"
        $ = "Set T452521 = J42_489(GetObject(\"winmgmt\" + \"s:Wi\" + \"n3\" + \"2_Pr\" + \"ocess"
        $ = "Set TGqwi = Shapes(\"kaqkDoaFLZ\")"
        $ = "Set TT9Nzj8 = GetObject(DjJziuOh(\"WinmGmts:Wi\" + DjJziuOh(\"n32_Process"
        $ = "Set VBIvOSN = PTidB"
        $ = "Set W1QGCAA = RA4o1AAw"
        $ = "Set Y348958 = GetObject((\"winm\" + \"gmts:\" + \"Win32_Proc\" + \"essS\" + \"tartup"
        $ = "Set YEwFOQ = EXcan"
        $ = "Set aGUDAA1 = SCoX4wAk"
        $ = "Set bznuP = JjiBCfs"
        $ = "Set d997_09 = c__4__4"
        $ = "Set fHw8Y7 = GetObject(np3LBb(np3LBb(hrP6Pc6 + \"startup"
        $ = "Set k68733_6 = GetObject(\"WiN\" + \"MgMts:w\" + \"In32_PRocEssStArTuP"
        $ = "Set kTaPWo = CnLaOM"
        $ = "Set l8847355 = GetObject((\"winm\" + \"gmts:\" + \"Win3\" + \"2_Process"
        $ = "Set o1845290 = CVar(w384214"
        $ = "Set q17811 = GetObject(\"WiN\" + \"mgmts:Win32_ProcessStarTUP"
        $ = "Set q88_524_ = E7649_84"
        $ = "Set q_96__86 = f2_61_30"
        $ = "Set rDBXwAA = zAAD1AU"
        $ = "Set u23261_9 = O56_43"
        $ = "Set ukMzHfzwu = GetObject(\"new:72C24DD5-D70A-438B-8A42-98424B88AFB8\")"
        $ = "Set uuiNz = mXTBlw"
        $ = "Set w617274 = CVar(S_803331"
        $ = "Set withdrawalvr = Borderspr"
        $ = "Set wmGBW = oWiwt"
        $ = "Set zU4BAAG = iUBDAA"
        $ = "Set zdXQr = vTqErLCw.Shapes(flIwQE + \"GFtELIoGcL\" + VzVDm)"
        $ = "Sgn(219063213"
        $ = "Sgn(742242790"
        $ = "Sgn(766559353"
        $ = "Sgn(79049772"
        $ = "Sgn(827388599"
        $ = "Sgn(980309105"
        $ = "Sgn(QADAUAUA"
        $ = "Sgn(SQAUABAA"
        $ = "Sgn(UAAkAA_"
        $ = "Sgn(ckXXAoxZ"
        $ = "Sgn(dQAAZAG"
        $ = "Sgn(qA4xAcAk"
        $ = "Shell GLa6B9u() _"
        $ = "Shell@ Shapes(JIjlVCZ + bbqtd + 1 + vUsEPo + EurUh).TextFrame.TextRange.Text + MLbZH + XXLwd, qjroTntk"
        $ = "Shell@ XZHzoVINkod + zpnSZbjhvf + ABUEzlL, Format(0)"
        $ = "ShoesMovies40 = clientdriven19"
        $ = "ShowWindow = CzcSzz + HciP0Y + wXqH3Nwj + RnjZh4 + wfsbbj6l"
        $ = "ShowWindow = vbFalse - vbFalse"
        $ = "Sin(3439"
        $ = "Sin(40016"
        $ = "Sin(40405"
        $ = "Sin(51902"
        $ = "Sin(60936"
        $ = "Sin(67009"
        $ = "Sin(74148"
        $ = "Sin(79118"
        $ = "Sin(81163"
        $ = "Sin(nNKzKh"
        $ = "SjSbAIlIYX"
        $ = "SkCDDowB"
        $ = "Sqr(177226476"
        $ = "Sqr(33931"
        $ = "Sqr(58694736"
        $ = "Sqr(688130465 / 214433976 + 151036199 * Atn(623335531))) + scAQGAB / _"
        $ = "Sqr(70008"
        $ = "Sqr(82937"
        $ = "Sqr(D1QAQA4"
        $ = "Sqr(FACA1X"
        $ = "Sqr(HDkADD"
        $ = "Sqr(fABU4DA"
        $ = "Sub ZFodZ_(YoPb3A"
        $ = "Sub love(BAqhCi)"
        $ = "SzjLFa = fzdzMD + UFsRoH / (uKXCzn * mqTPRp / jBYai / RBlHlv"
        $ = "T91451 = d0565_.E_55377 + d0565_.k1_257 + d0565_.L278159 + d0565_.a28_1645 + d0565_.s251921"
        $ = "TDDZkUCC"
        $ = "TFdawQQq = \" 48 \" + \" ^\" + \"+31 \" + \"49 5"
        $ = "TLkqFvFXrC"
        $ = "TNJRMYQUz = CByte(RBmmfmi"
        $ = "TNQQZODP = CDate(oBXEP"
        $ = "TPYzz = voAWJ * CDate(OWKJGcDj * UThluYB) * zEofGZ / Sin(iCDERST) / oGSIj + 212963522 - 135590833 + Chr(66556945) + (uj"
        $ = "TQjbQXhsWBi = \"F^d^a^o^ln^w^o\" + \"^D^.^I^ED^$^{^yr\" + \"^t^{)^H^b^X^$^\" + \" n^i^ ^i^K^h^$(hc^a"
        $ = "TQvSiMnEvOhDjhNbzN"
        $ = "TSsjHl(0"
        $ = "TT9Nzj8.Create jIR3nwEm + DjJziuOh(\"pOwe\") + zlENFnFB + Swlfot9.FbFMIBR + Swlfot9.bLJAPOF + CfCOp_Y, k4uZjr6l, vKdiFDQ0, Y2kmwLH"
        $ = "Tacticsat = CLng(874"
        $ = "TalOT = Sin(68211"
        $ = "Tan(\"wvLq5v"
        $ = "Tan(204124700"
        $ = "Tan(789194994"
        $ = "Tan(MQGABUAA"
        $ = "Tan(l_2494_2"
        $ = "Tan(u_6_65_2"
        $ = "TiAqLihdtTJaiIwNipM"
        $ = "TiSBk = Hex(QrzZKWaip)"
        $ = "TnAkb = bsoshUSH + VBA.Shell(IEYZXV + Chr(WqvbPTbYGtP + vbKeyP + aJNTzabBdkw) + \"owers\" + vvhoiOiw + MBMLXoVl + coAVE + hcqsppXq"
        $ = "TtCJquKuF = CLng(ZPYAPs"
        $ = "TypeName Atn(12173541"
        $ = "TypeName BhkJRp"
        $ = "TypeName BjJbO"
        $ = "TypeName CBool(846"
        $ = "TypeName CBool(WoNNhX"
        $ = "TypeName CDate(3"
        $ = "TypeName CDbl(ciJdE * GadXB - 66969 - TXpEQd"
        $ = "TypeName CInt(129571863"
        $ = "TypeName CInt(jpjAXt / DCiHzw * 68372 / nBGmSt"
        $ = "TypeName CLng(aNEtjX * FwwzB"
        $ = "TypeName CSng(odsAn / nnIun - 62747 * AazXY"
        $ = "TypeName Chr(65079 * MZnjWH"
        $ = "TypeName ChrB(12643571"
        $ = "TypeName ChrB(28812393"
        $ = "TypeName ChrB(mwIqhs"
        $ = "TypeName Cos(849"
        $ = "TypeName Hex(28943 * wBnoNN"
        $ = "TypeName Hex(Buvla - LzJBCr + 86886 + SQEtWv"
        $ = "TypeName Int(5219"
        $ = "TypeName Log(271265640"
        $ = "TypeName Log(6627"
        $ = "TypeName Log(68150 * srHzGq / liBjd - 77025"
        $ = "TypeName MazADN"
        $ = "TypeName MwKAa"
        $ = "TypeName Oct(WqkcA"
        $ = "TypeName Oct(qkhibX"
        $ = "TypeName Sgn(30196 * WcfmBb + 78661 * KtjhJ"
        $ = "TypeName Sin(4"
        $ = "TypeName Tan(4"
        $ = "TypeName ijKUzj"
        $ = "TypeName jGsku"
        $ = "TypeName tAYwtF"
        $ = "TypeName tFWYT"
        $ = "TypeName wXuiE"
        $ = "TzNEj = DtPXw"
        $ = "U07210. _"
        $ = "U0837 = 148219679"
        $ = "********"
        $ = "U308742 = ((\"o33805\") + (\"953540693"
        $ = "U945 = h335"
        $ = "UABAUAxU"
        $ = "UBbkwz = kqETsU * tdzTS / 32879 * 44121 * (XGDaA / 23462"
        $ = "URcazcVmnTdNzu"
        $ = "UUYnuVaT"
        $ = "UVqVdR = (41602 - DATKV * puwVI + YbJEb * (PnKJF / QldRI * wEzuJt + wCRiAv"
        $ = "UVsjz = 28544 + NfIdlS / (30441 / WYVit * OdOAsl / dzzKu"
        $ = "UfcDWpttovZKopULFFEDiRVVFTFj"
        $ = "UrwaUCWND"
        $ = "UuwItiEVVRI"
        $ = "UzvNnVNPBJ"
        $ = "VAAACAAw"
        $ = "VAAcU1U = hQUAo_QA / 948914106 / dABUAAAx - CInt(u4BAxD + CInt(828975253)) + (241921069 * CLng(191312248"
        $ = "VAUBUc = Sin(SAAAAoBQ - Hex(jACC_Zw"
        $ = "VA_DAZc = Asc(796066926 / Oct(289573419"
        $ = "VBA.Shell$ \"\""
        $ = "VCAAAAUB"
        $ = "VDckqiuQlMQLRWsuuJNmLw"
        $ = "VDwFcJ(1) = Left(TSwnWHrpDjYUR + CjEqmkVGYROqtGirqIZjKfdLzmvMz + PwnGEGzLaPEtA, 913) + Mid(CVZXIcw + IZWTcLTmiHvQtltwFldPCltqXE"
        $ = "VGFXdPcBsCTsjGEwNL"
        $ = "VIMiZF = (97630 * TuLjBZ - 5996 + FiWWa * (36780 + qzijJ / LBunlj / OhVQk - MljAYM * JYOizS"
        $ = "VPVHwbTknfBddKCFlbjRS"
        $ = "VhoEiX = \"^im-\" + \"hca^b\" + \"hcsi\" + \"^\" + \"f//:\" + \"ptth@\" + \"^m^yv\" + \"a^3^\" + \"HQ/^moc\" + \"^.^yr\" + \"tn\" + \"u\" + \"^oce^h^"
        $ = "VhzadA = nOijPY"
        $ = "VucvoWrMG = Int(327251806 * GXAEi"
        $ = "VwrwVnYVP = \"n^e^i^lC^b^e^W^\" + \".^t^eN^ ^tc^e^\" + \"j^bo^-^w^en\" + \"^=^I^E^D^$^ ^l^l^eh\" + \"^sr^e^w^o^p&&^f\" + \"^or /^L %^W ^in"
        $ = "W788626_"
        $ = "WA1AAACA"
        $ = "WBUzimvvWWf = buSHckDu + JZuFw + RwZzjtBObYt + MEdnBRO"
        $ = "WC1oUoQD"
        $ = "WC1oUoQD = 974052615 - Atn(884252468) / 510003871 / 501545027 * 482390539 - Rnd(YAQAGBA / CSng(173755901"
        $ = "WCSHPu = Sin(64895"
        $ = "WNFDOrQaD = Cos(WGfnolpZ"
        $ = "WZA_U1B.dAZU_Ao"
        $ = "WeekdayName E5229195 + H32_86 + (A5219665 / 829212494 + (Z97207 - Hex(R18478 - z1440__ * d_8784 + Cos(R546234 + 557687998 - 2784"
        $ = "WeekdayName j70602 + l625278 + (R98800 / 697982226 + (R61082 - Hex(r64945 - s44915 * w__3798 + Cos(m08599_4 + 975800293 - 807368"
        $ = "While A52092 And 123278912"
        $ = "While A552_695 And M50_440"
        $ = "While B10103 And 623024061"
        $ = "While B68171 _"
        $ = "While C84614 And 391229455"
        $ = "While F13687 And F9100321"
        $ = "While J184521 And X811710"
        $ = "While J773572 And X119_84"
        $ = "While K1194093 And 118413774"
        $ = "While Q6__7125 And X891124"
        $ = "While T3127936 And 322884932"
        $ = "While U41094 And n666_59_"
        $ = "While V996_593 And 356701372"
        $ = "While Z51802_ And z_4_852"
        $ = "While Z__39771 And B4_5201"
        $ = "While b24440 And 459520417"
        $ = "While d_56547 And i4035254"
        $ = "While h17136_ And F8488805"
        $ = "While i08528 And 552808729"
        $ = "While k267_02 _"
        $ = "While k557156 And 564038286"
        $ = "While n050064 And 376973578"
        $ = "While o9842007 And b5854_"
        $ = "While s675_4 And 796560502"
        $ = "While u8669441 And 550045514"
        $ = "While w886_63 And G468873"
        $ = "WpDbq = \"^44 \" + \" 54 \" + \"5\" + \"^6 , \" + \",^ \" + \" 1\" + \"8^\" + \" ; 2\" + \"7 ^,\" + \" ^\" + \", ^ \" + \"+54"
        $ = "WqzTkt = CBool(zwRURqTRI"
        $ = "WsMIJ = 338699924"
        $ = "WtuPNhEr = 180645320"
        $ = "WwNEzJwNhR = \"44, \" + \"118, 52, \" + \"57,49,6\" + \"2 , 56 ,\" + \"47 , 123,\" + \"21,6"
        $ = "X328722N56484r198193T446_2"
        $ = "XCRWG = CDate(ipwztL + Sin(40127 + 48104) * 41321 * CInt(29695"
        $ = "XImYc = CDate(wviTFA + Sin(84042 + 85632) * 70115 * CInt(18596))"
        $ = "XPFhr = \"^\" + \"HA^0\" + \"^BAa^\" + \"A^\" + \"AEA^0^\" + \"B^gS\" + \"^AIEAW\" + \"B^AcAY\" + \"^FA\" + \"0^B^\" + \"g^Z"
        $ = "XVlGCOMbM = fFConD + PjoVjN + oTdNuFfD + GSwFIsVVcZ + JFCSjfzFl + hRLzJjVzRHw + qrAzwm + oDNXD + DlLbaKY"
        $ = "XZHzoVINkod = NZbbM + kwBWK + DcuvHZU"
        $ = "XisID = VQJpkw / ojwEO + cwAnu - vMTznC - 80441 + 30798 * 67206 - GPpOY"
        $ = "XtXItXNP"
        $ = "XwkGAX = lDCAAQ + CInt(M14ZXQC) * 643063684 * CBool(896899234) + 211300330 / Round(iAAAUQD) - sGcAUAo + Sqr(328542880) - 3776862"
        $ = "YAAQGXAB"
        $ = "YDA1xQ = (971282489 + Rnd(iAwUCAxB * _"
        $ = "YKBpOUFmamujzzPNs"
        $ = "YMTiBY = (51321 * vWbiwO - 37845 + kdBEPS * (14591 + fCzTV / ABtfbM / RwoMb - dvIMi * USwRK"
        $ = "YNzoaWiSpXj = Format(Chr(11 + 13 + 1 + 7 + 67)) + \"md /V^:^O/\" + Format(Chr(8 + 9"
        $ = "YU_BUA = \"ZABlAGYAbABBAHQAZQBzAFQAcgBlAEEAbQAoAFsAUwBZAFMAVABFAG0ALgBJAG8ALgBNAEUAbQBvAHIAWQBTAFQAcgB"
        $ = "YWlNiGRj"
        $ = "Y^7^;^2^6n"
        $ = "Y_9_5145"
        $ = "YiZNCb = \"Hpno"
        $ = "YkWSwjtA"
        $ = "YmoODQ(0) = Left(dwQtOQ + pQXrWnGPpqzaNNVHJZ + owmsiz, 763) + InStrRev(OGIJS + hzhzNZuPUHftJczivjwIHb + jTESqLw, rvFOw + KrHdAlf"
        $ = "YnwvsjHluT = QStTbE + KlEoNu + MUvvlZJJ + OEsbQt"
        $ = "YuPaY = tQTEn - rfBzXE - 83931 * fOnFu * wmUPZ + bIKkG / (oadfLE / GTjidG"
        $ = "YvJuaRGFmtdMGdNYPrTS"
        $ = "YwZ_ZZB = 724234975"
        $ = "Z5719927"
        $ = "Z6_67243M94648X_6266s_3368"
        $ = "Z7953714"
        $ = "ZKEBf = CDate(62560"
        $ = "ZU1xZD.ZZUUAx.Text"
        $ = "ZVDAjzu = WvjwHIu"
        $ = "ZbGDv(1) = Left(\"JnZsMw\", 404"
        $ = "ZjXwAQLQS = \"Hel\" + \"l \" + \"("
        $ = "ZjYmJtnTi = Hex(uLftwisf)"
        $ = "ZjfUnXkLQ"
        $ = "ZnjqbD = 51374"
        $ = "ZvvzpG(2) = MidB(Pmlbl, 230, 883) + MidB(Pmlbl, 230, 883) + Left(bZGoFYi, 398) + Mid(PVPjOoqM, 329, 335"
        $ = "^.^g^e-t^i//:^ptt^"
        $ = "^3^41^;-^1^"
        $ = "^=^8)^s^H^q^Lf^h^5"
        $ = "^K^h^$(^e^l^i^"
        $ = "^e^h^sr^e^w^o^p&&^f^or"
        $ = "^ej^b^o-^wen=^MwH"
        $ = "_e/^-_W-"
        $ = "a0630155H37561D7_1927i09_2_"
        $ = "aAAAAAAX = ECw44o _"
        $ = "aCLElTN = \" \" + rzIomYCmv + BUicaEfwHES + \"{ N\" + XhoYVPWfjiGA + BNFjYQnYv + \"EW-\" + PRYKVKZmZQF + QjOzVqYbbmd + \"O\" + Vttvwln +"
        $ = "aEwWFLcmPsO"
        $ = "aPiANuqmWvB"
        $ = "a_AcAA1A"
        $ = "acvzzRBDzz = \",\" + \" \" + \" 26\" + \" \" + \" \" + \", \" + \" 2\" + \"4 \" + \" \" + \",\" + \"55\" +"
        $ = "adGjw = 72345 / caiuj / (lIztM * nwfLPk / 73400 - wAvdsI"
        $ = "ag0a27g720aia0"
        $ = "aiDWoLFl"
        $ = "apiYPNQwKiJCjzEUdnYq"
        $ = "asIFDlHbbDhcXCDKzrtErujd = 259126596"
        $ = "asvJq = (VSYLtc * isXQK) + 72840 - BojsAt * RzSrZK * 14469"
        $ = "autoopen( _"
        $ = "awoBADDA"
        $ = "b308_3_3"
        $ = "b602663. _"
        $ = "bBQAQCQ = CInt(864248896 - CDbl(QCUw4CA) * pZXZADQ * 746553661"
        $ = "bGENlBIUr"
        $ = "bHTMW = \"^\" + \" ^37\" + \" \" + \" 29 \" + \"+^64 \" + \" \" + \" 2^1 \" + \" +1\" + \"7 ^\" + \" 2\" + \"6"
        $ = "bQnMNMwC = \" \" + \" 5^5 \" + \"^\" + \"+37 \" + \" 36\" + \" \" +"
        $ = "bVoLQjGZG = Hex(otIIJQI)"
        $ = "bXcrBA = Sqr(8"
        $ = "b_0_705_ = Sgn(700430210 * Round(672395957"
        $ = "bacPsi = zGFYtu - LLzqAr - (19781 * bkhmQa"
        $ = "bcVWKKrvkFoWLnJ"
        $ = "bfBvpVnaCJvpNbkJBJpN"
        $ = "bjPczw = CStr(lCwjNk - pRIKO + 32230 * iWzcIF"
        $ = "buSHckDu = \"HeLL -e IAAuACg\" + \"AKABnAGUA\" + \"VAAtAFYAQQ\""
        $ = "bwsQOfvjNS"
        $ = "c156_430"
        $ = "c1666409"
        $ = "c3__9_54 = z672809"
        $ = "cAkA1wA1"
        $ = "cBija = 83888"
        $ = "cCEuU = Log(315038804"
        $ = "cGCUUZ1A"
        $ = "cGDABAQ.OQA1XAD"
        $ = "cHcJtNGRt"
        $ = "cIAIKZ = Tan(19588"
        $ = "cLYiAP = \"mD\" + \" /v^:O^\" + \" ^ \" + \" \" + \"/r\" + CStr(Chr(ZlQoajHzhIzP"
        $ = "cQQtmj = kzznBq"
        $ = "cYaDOlKZwKrnupvdiTK = 296641512"
        $ = "c_732269"
        $ = "caSdwLJz"
        $ = "cc4occUo"
        $ = "cqPMiu = PcfwCV = UMqkua"
        $ = "czjHkZ = zZrWzI - UfjKfS - JrklB / LzswG + ujsXU / vNwZXd"
        $ = "czvLCwz = Hex(TtBpXh"
        $ = "d391943 = (p_3_4494 * Fix(399737514 / CBool(j_2_820))) - S385003 / Oct(656307157) / 921253193 + CStr(p012_194) - 580513518 + Chr"
        $ = "d520432 = \"********"
        $ = "dAUBAo4w"
        $ = "dDlXjF(0"
        $ = "dGA4xAAx"
        $ = "dGAZGX = Atn(294911941"
        $ = "dJNYo = hSSkPH + YRAdHC + lzDoRt + FfYNt"
        $ = "dJhYWGlWj"
        $ = "dKofZ = 36680"
        $ = "dWGCY = HKtwdM + fRMMwK + zcmvp + oPtbvB"
        $ = "dXahi = (18335 - YkOYKL - WtniX / UVXbS / (jkuff - KBPjh * (aXnaE * zbjKzY / rrVDHz - OZapiE"
        $ = "dkDx_UAB = nAowAAw"
        $ = "dlpCMXIVLXv"
        $ = "duKLJpbGHz"
        $ = "dvpCY(0) = 192"
        $ = "dzQzEHobo"
        $ = "dzcukrklkq.js"
        $ = "ebusinessbs = CLng(315"
        $ = "f0937__6"
        $ = "f7121435"
        $ = "fC4BDkcX"
        $ = "fDQBw4A = w1o1CU"
        $ = "fFXswVSz = \";73;46;\" + \"35\" + \";62;65;\" + \"5\" + \"3;43;\" + \"35;47;24\" + \";39\" + \";\" + \"50;71\" + \";47\" + \";31;3\" + \"0;41;73"
        $ = "fIULj = CLng(112302493"
        $ = "fLvEGtwatzv = \"^7\" + \" \" + \"^\" + \" ^;\" + \" \" + \" ^4\" + \"6 66 \" + \"5^4 2"
        $ = "fQkCAQUo = Atn(875120804"
        $ = "fZUDOrFpi"
        $ = "fcZXB(3) = MidB(shwKz, 567, 135) + Left(iMCJl, 246"
        $ = "fnijADztF"
        $ = "foPrhlj = Array(Uahrw, QlnKdM.Run!(nfSRnplJZj, swDPHCRc), jbESGbNS"
        $ = "fwAG_G Then"
        $ = "fziowVMn"
        $ = "hBt7YVKrp"
        $ = "hDDAAA = 796886080 * vDAUAc"
        $ = "hDtid(1) = Left(MadVr, 801"
        $ = "hKMbU = 136995925"
        $ = "hLAsT(0) = Left(qoNXlsT + jDHpESOokrLaPwuQtnw + fudMNJQf, 495) + Right(JwrJO + EjzUPYAmzDdjTJJpTAiT + RGDXHlB, 260) + InStr(cqKf"
        $ = "h_0029 = 621413701"
        $ = "hiUfG = \"AKwBbAEMASAB\" + \"hAFIAXQA2ADg\" + \"AKQAsAF\" + \"sAUwB\" + \"UAHIAaQBu\" + \"AEcAXQ\" + \"BbAEMASABh\" + \"AFIAX\" + \"QAzADQAKQAuA"
        $ = "hmZcW = Amjms + BUITF"
        $ = "hsjnq = 35944 - wiiLL * (wSCmM + bUlDrX"
        $ = "htRcaonnVjp"
        $ = "hvGqSVGBL"
        $ = "hzSRwBQJvlVd"
        $ = "i44302__"
        $ = "i4736 = Round(G8317 * Chr(120032450"
        $ = "i61663 = \"v8286705"
        $ = "i659_4.P4905_5.ControlTipText"
        $ = "i659_4.P4905_5.PasswordChar"
        $ = "iAAAA_CA"
        $ = "iBDAx1A = hCACZQ + CInt(Y4kAA4B) * 331553435 * CBool(133457324) + 103108128 / Round(kAGQADX) - PkUDkA + Sqr(127921634) - 8396488"
        $ = "iDwZAkA = ZBcB1Aw + CInt(wkAXGAA1) * 84471752 * CBool(442863379) + 962353833 / Round(AAADZA) - YwDQZBB + Sqr(231634437) - 692052"
        $ = "iGOCButAozMq"
        $ = "iGUXCABA"
        $ = "iJplwl(4"
        $ = "iQ4U1QBw"
        $ = "iQAZXDAD"
        $ = "iWBAWFDd = \"2;75;1\" + \"1;50;\" + \"72;12\" + \";12;56;\" + \"67;1\" + \"5;61;44"
        $ = "ia\", \"j\", \"a\", \"ps\", \"qw\", \"uj\", \"H\", \"h"
        $ = "icAAA4Ak"
        $ = "idnxQHzVi = Sgn(59371.484568226"
        $ = "input37 = deposit7"
        $ = "irCaJqwnP"
        $ = "irjGvvJKfEAbKhnopSzin"
        $ = "ispqD = CByte(325434282"
        $ = "iuUrOoiEw"
        $ = "ivjZO = Rnd(DKCisVuq"
        $ = "iwiPh = CBool(PQJFqJYU"
        $ = "izvchwfOiLm = \" ^2\" + \"3 ^ +\" + \"^\" + \"8 \" + \" \" + \" \" + \"+53\" + \" \" + \" +"
        $ = "j052640.A64818"
        $ = "j0_467(87197) = 352 + Int(A9_904) + J774001 + Int(482) + F04693 + j2580329 + 716 + z79189"
        $ = "j137668(18360"
        $ = "j3__8_38"
        $ = "j9___593 = 818566206 / Hex(d00_68 / Chr(E_0_2___ - CDate(104074348)) * 14131296 / 249637184) / Z6490420 - Fix(158767150"
        $ = "jABAcxXC"
        $ = "jMpjlZ(1) = MidB(SuJNj, 950, 667"
        $ = "jRVab = 30665"
        $ = "jWXajuuZfGWwouzzS"
        $ = "jXaIwkEK"
        $ = "jZFZGdcAvCiNLt"
        $ = "j_1_8826 = 26966254 / Hex(Y958__7 / Chr(E6_18_47 - CDate(474351511)) * 825416901 / 624998260) / C_9_09_ - Fix(849718355"
        $ = "jankksV = \", ;^ \" + \" ,\" + \"^\" + \" ^ ^\" + \" ^43 \" + \" , 3\" + \"^"
        $ = "jjTajD = \" u\" + \"e\" + \"C7+\" + \".gy\" + \",$Q\" + \"&&f\" + \"o\" + \"r\" + \" %S"
        $ = "jkVzi = \"w"
        $ = "jnSfp(0) = InStrRev(PVMpDJi + rwUtOzVVzIhXqkmCl + aUHwtjA, TdVmO + zXqzJsJzoozTXjXWzlSP + XnoJJv) + InStrRev(nmivid + KLEuAlnFBh"
        $ = "jujpOz(0) = 506070408"
        $ = "jvjapGTWD = \"OwerSHell . (\" + \"(VaRI\" + \"ABLe"
        $ = "jwcZoBkk"
        $ = "jxZxB1AB = Chr(UBwUcABo"
        $ = "k1QAUc = CVar(723354492 * Rnd(QQAkZAU * Round(721449592) / 21190537 * CLng(787349579 * Sqr(lco4ADo"
        $ = "k9UYoYwE"
        $ = "kA1AoAB = Tan(SUADAQoU - CSng(XBBAQkX"
        $ = "kAAAAA_U.jxcAxCA"
        $ = "kAD1DDBA = (T_XA1A1A"
        $ = "kSBEvQL = CBool(CzPLrC"
        $ = "kZAUxXA = zAAXAA1 + ChrW(cADGAk) * 960192802 * CBool(786191768) + 643047316 / Round(wQUACxDc) - A_ZAAA + Sqr(349236187) - 895928"
        $ = "kZCwAAo = Sin(829613004) + CSng(674709657"
        $ = "kcUXA11Z = _"
        $ = "kcZrVQn = \" \" + \"^3 +4\" + \"^4^ 44\" + \" 4^0\" + \"^ 6\" + \"^3^ \" + \"^ 48 \" + \" ^"
        $ = "kjkKW(0) = zHodID + QIOww"
        $ = "kkrVlLLLN = 251328809"
        $ = "knYApNAJ"
        $ = "koKctwoEpW"
        $ = "ksBozQ = \"\" + lkpvoSwQ + zzThFsUbYhGdWZ + \"w110\" + tiudJsCvWUjtWi + wCrIrsiOBq + \"z32\" + AINPJrYPW + GvXKkJbEqNhiT + \"m36\" + EBE"
        $ = "l87G86_64l66w65"
        $ = "l8847355.Create A_33800 + P39242 + i665411, r2795657, c_61385, B45219"
        $ = "lA4G4A1 = 139668389 - 640127231 + _"
        $ = "lA_AA_U = fUAXUA - BAZDco1"
        $ = "lDkMvzTaVGFziNRlRJvOsip"
        $ = "lGGo8AsL"
        $ = "lHwRi = muTPLk + CArSjA + cSXzLD + Yzcttb"
        $ = "lK4vR5\" + (\"989\" + \"757\") + \"Z8VBGR\" + (\"wGVXaG"
        $ = "lcUc_QQQ"
        $ = "lqaKKD = \" ,126 ,42,8\" + \"9 , 115 , 121, \" + \"126 ,111\" + \" ,103 , 36,68\" + \" ,\" + \" 1\" + \"11,126,36,93\" + \", 1\" + \"11,104 , 73"
        $ = "lssEr = \"zFNO"
        $ = "m1463_41 = ((\"q70184\") + (\"240575542"
        $ = "mBB4AQ = CVar(YDAAAA"
        $ = "mHVqwl = (OwaRDC / VWqcP / 84651 / EiBMZ + 58302 * IFjGu / 13241 * jjtzFH * (hHCBj - jqCLv"
        $ = "mHbaikOrPnUzFVrBGJit"
        $ = "mRmSnzlJA"
        $ = "mTvTcDpC"
        $ = "mZAAAkAA"
        $ = "mbsOjpdHAzzbmnKkNKNsJ"
        $ = "mbtRiAuK"
        $ = "mgmts:Win32_ProcessStarTUP"
        $ = "miUdd = JEfhs - lJWSDE / 18086 + XDaab"
        $ = "mjUiWuWoqvsXSzwVfZPQvFiGwAIP"
        $ = "mlBiZ7CA"
        $ = "mlMYidDNAvwzNFpnhmwrknw"
        $ = "monitormj"
        $ = "muLbOK = 66528 + wjizd / (51829 / aMzaj * jsJrs / FwzLn"
        $ = "mwTIp = \"leh\" + \"^sr^e^\" + \"w^op\" + \"&&^f^\" + \"or /^"
        $ = "mzwitB(1"
        $ = "n30164 = w__0857_"
        $ = "n32_Process\").Create"
        $ = "n4ABowAQ"
        $ = "n5685_52"
        $ = "nAABADAG"
        $ = "nAAQBUAQ"
        $ = "nAC8AcwAnA"
        $ = "nAUcxB = jC_AAC + ChrW(FUXkQAZA) * 110314554 * CBool(966716839) + 567144626 / Round(rA_AAQA) - RkCBko + Sqr(953857155) - 3285409"
        $ = "nA_xBDAU"
        $ = "nDkBG_i1"
        $ = "nQAUUA1A = 449787714 * fAA_QAA"
        $ = "nWnfBnjdjTEZkt"
        $ = "n_0758_0"
        $ = "n_5462_ = \"KwAnAC4AM\" + \"QA0ADgALwA1AEMA\" + \"VAAwAEI\" + \"AJwAr\" + \"ACcAQwAn\""
        $ = "navigatelh"
        $ = "nivEzTYtDDfM"
        $ = "niwjTDlMz = Rnd(HtwsldoV)"
        $ = "nnirGkPwq = Log(95212520"
        $ = "o716__b3_680F97_168Q4312_12"
        $ = "oAZACAxZ"
        $ = "oILOuavQqjmIdBz"
        $ = "oQqSLm = (54817 - horYVp / (87510 * JqZlI / 59419 - wzoTMU"
        $ = "oVszCbfj"
        $ = "osjZOrwOZfkY"
        $ = "owBDAxAA = ZCA_BZ / 198442998 / _"
        $ = "owjIl = rFlOWTVDplz + FuqMLjzcR + zskaN + tQTdBIuqZwS"
        $ = "owjuirkvizs"
        $ = "p17935__"
        $ = "p219465(87302"
        $ = "p4_40217"
        $ = "p5874_ = \"597583752"
        $ = "p5EhmDP0"
        $ = "pJWBQb = 88288 + Atn(39942) / 63460 / Round(13327) / 7065 / CInt(DPpYT"
        $ = "pOCom(2) = Mid(fmwSuvRQ, 102, 719) + Mid(fmwSuvRQ, 102, 719"
        $ = "pOoHiw = HQdGRFc.ContainingRange + LiwHAuhw + JirorIHQ + OjzKiGwW"
        $ = "pSOwXzJ = uozJ7uv(uozJ7uv(\"win\" + uozJ7uv(uozJ7uv(\"mgmts:w\")) + \"in32_process"
        $ = "pUrjuOOoRY"
        $ = "pVwAUC = CDate(71034"
        $ = "p_1965 = vbError - vbError"
        $ = "pixel18 = GB39"
        $ = "pjYpVUD\" + (\"479\" + \"370\") + \"tI3czL_7\" + (\"NiA6_i"
        $ = "ptSQnhhknbabsDVBArlzUK"
        $ = "pztzj = ITzTR + RzCJVzR + dQlMJ + szqwpiP + iOHAbBX"
        $ = "qAUQ4BUD"
        $ = "qA_A_AUB"
        $ = "qBtVoMDADiFCvPIDYimjBsTGvwT"
        $ = "qMqJiEcwFfX"
        $ = "qPtYcqaZH"
        $ = "qPuCVTbq"
        $ = "qRiRww(2"
        $ = "qUAQ1A_Q = ZB_Bo_ + ChrW(qAUwACUG) * 434256303 * CBool(437773829) + 859097120 / Round(OXAX41UA) - lcAQQGDU + Sqr(794212281) - 29"
        $ = "qftnh = \"bbzcPkwY"
        $ = "qhmitpvuUJbLKDfKXGdzrBs = fLzwjfhnGfwGZZw"
        $ = "qiAAqY(1"
        $ = "qiAAqY(2"
        $ = "qjzcp(2) = 74"
        $ = "qkZ8CbZK"
        $ = "qrZAzHuCdIpMoicBUuoCSdKB = LDjKFYFBWtsRzVtNwHzKwb"
        $ = "qvwdCYDianKaMpEYouFpERA"
        $ = "qwZbYrSV"
        $ = "qwcZAD1A"
        $ = "r62893 = ((\"I28954\") + (\"659480211"
        $ = "rABXAAA = Round(OkAAQU"
        $ = "rDDMzX(0) = jmiEjf + AptiXt"
        $ = "rFwJBNF = CByte(225624211"
        $ = "rRRuwCLnEbsuEnojGwWFICIEZ"
        $ = "rUEXzd = (GpPcj - MiwGY) * Mraiv / ssGCvX / 93877 * Gzzczk"
        $ = "r^o^tc^e^h//^:^p^t^t"
        $ = "rej3u7Q = Round(19909.986118677)"
        $ = "resources/tyvldlikgp"
        $ = "rhNmWZ = FmpcmR"
        $ = "rkjsFtIvcwDGWw"
        $ = "rlnzQS = bdolG + UlNMi"
        $ = "s360_275 = ((\"m1_025_\") + (\"720877536"
        $ = "s6397073 = w660__5"
        $ = "s:Win32_ProcesSstartup"
        $ = "sAZU_AQ1"
        $ = "sBAAACAZ"
        $ = "sLRuWqAmV = Array(EPcWu, DhjiWP, uMvMh, Interaction"
        $ = "sNGKFzkiTVM = \", ; \" + \"G\" + \"e^Q \" + \",\" + \" ; 7^1\" + \" , \" + \"; ( \" + \"(\" + CStr(Chr(tTYDsZXDMoaDiZ + cbOSAIV + 99 + iwzpTW"
        $ = "sOX5r1 = DYnFYn + \"win\" + I_0KNb"
        $ = "sTowj = izkokRnI + kcatE + oDNLTcR + VbjKlwRsjHL + ljSQsvtp"
        $ = "sXUZABAB"
        $ = "sYCizskD = Hex(ptmhpNW)"
        $ = "sYbfXo = 91898 + kaDczc + (70027 * CDbl(EzzVPQ) - RYoZs / CSng(39930) - RrTEf / Hex(QPEiKY) + 94379 - 59598"
        $ = "sZLoOrifv = \"fQB7ADgANwB9\" + \"AHsAOQAzA\" + \"H0AewA3A\" + \"H0AewA2AH\" + \"0AewAxADIAf\" + \"QB7ADcAN\" + \"gB9AHsANQA0A"
        $ = "saJXjomUOMOwEl = ChrB(327660076 / ChrB(59848184))"
        $ = "siWzCGiO = ZjXwAQLQS + djrPUZFuM + BclUwON + SukzncS"
        $ = "sifxsbscux/Mzvrvrnidsv"
        $ = "soHZJH = 30973 + DZXmU + (99000 * CDbl(nFpqX) - jMCZM / CSng(10566) - QXnRNK / Hex(TaNjB) + 9437 - 5465"
        $ = "ssUp3vV\" + (\"115\" + \"990\") + \"Su7RZqP0\" + (\"ki7l0aC"
        $ = "stMiVSQOwSfBctplTTMoTTSI = 63283541"
        $ = "synergies78 = Array(Steel97, userfacing1, Park30, reciprocal90, TurkishLira30, dynamic33, wireless52"
        $ = "t019931z_7560T5172858J66671"
        $ = "tAcAAAXA"
        $ = "tAw4DAcA = AAwoDoD.AD1AA4Ax + A4_xwQG.DAZAAAA + AAwoDoD.AD1AA4Ax.PasswordChar + A4_xwQG.NXZCk4Q + AAwoDoD.AD1AA4Ax.PasswordChar"
        $ = "tDwDTjjp = CByte(278665600"
        $ = "tGZWDTiw = \"^i^f %^D=^=^\" + \"0 c^a^l^l %^o^\" + \"3^W:^~^-^3^7^5%\" + \"\"\"\" +"
        $ = "tUC_ZCUA"
        $ = "t^ac^}^;^k^a^er^"
        $ = "tb5jnov = zUATc971 + \"win\" + vOC56Bj"
        $ = "tfzCc = 65748"
        $ = "toC4_XQB"
        $ = "tsTJGE(0) = 556"
        $ = "twEmzUmLv = RWXqI + kwYKPS + HoFsiaonkoD"
        $ = "u033_53 = \"298163728"
        $ = "u34_959 = (k__5_51 * Fix(652825974 / CBool(v0140_))) - P_621_ / Oct(*********) / ********* + CStr(l273__8) - ********* + ChrB(F5"
        $ = "u3609229 = \"s4740_67"
        $ = "u3661479"
        $ = "u4PIMv\" + (\"593\" + \"875\") + \"dHLua5m\" + (\"sURr9Hjj"
        $ = "u734189 = *********"
        $ = "u78_70_ = (h_72239 * Fix(********* / CBool(n855_662))) - J31638 / Oct(88853536) / ********* + CStr(p0__2_) - ********* + ChrB(j_"
        $ = "uAEMAbwBEAEkAb"
        $ = "uDAAAAAA"
        $ = "uEaCPphzkHrHKcM"
        $ = "uQAorGNZjtwkMLvtaXi"
        $ = "uQTJMpiif = \" \" + \" ^30 \" + \" \" + \",^"
        $ = "uRDFZM(1"
        $ = "uUAoQDA = mXUA_Bo + Int(********* * Asc(jD1k_BD) + NAXAAAB _"
        $ = "uYlnFkjui = Cos(OlSaauC"
        $ = "uZVQUw = 90142"
        $ = "ubiihJqs"
        $ = "ubqwM = Array(\"Sp\", \"r\", \"dj\", \"Y\", \"z\", \"O\", \"D\", \"qH\", _"
        $ = "ucPurE = (ADlOam - icfvLU) * HXaIT / ojYwAa / 38012 * XAfho"
        $ = "ujHOZYuUzwX = pZaXc"
        $ = "ukMzHfzwu.Run@ WfOWA, tcRWMjZ"
        $ = "unAvFQTdh = \"8;38;1\" + \"3;5\" + \"9;59;0;7\" + \"3;71;71\" + \";59;60\" + \";30;\" + \"24;60;20;\" + \"60\" + \";36"
        $ = "uxCDQAAC"
        $ = "uznIjOKPmOnYku"
        $ = "v1991998"
        $ = "v4559575n05_89t230_91q5396_"
        $ = "v649439 = \"RsHeLl -nop \" + \"-e JAB\" + \"WAF8AN\" + \"AA0ADcAXwA9ACg\" + \"AJwBM\" + \"A\""
        $ = "v8_08__ = *********"
        $ = "vAAACwA = d1kw_c + ChrW(EZDUAwAc) * ********* * CBool(46789863) + 52370290 / Round(uwcAxxB) - RA_AAZZ + Sqr(812316541) - 7839888"
        $ = "vCjnYXQlTnRw"
        $ = "vIkXYCiCiJdfrH"
        $ = "vVslSIQKXpkfFPIaqovQfEn"
        $ = "vdiiYOBwhmkTijMVrRJpI"
        $ = "viVmW = PYccfp + UvCjBz + JHMHj + FzijCf"
        $ = "voA1BQAo"
        $ = "w4702276 = i__170 + (U44467) _"
        $ = "w70886(18360"
        $ = "w841331t8278390K032708o59372_"
        $ = "w8__2381"
        $ = "wAA1AACD"
        $ = "wAcAQ14k = 685405474 * OAxAUA"
        $ = "wCzRSI = 3632"
        $ = "wDAA1ckC"
        $ = "wIRbd = CLng(ThGiiHEYn"
        $ = "wOfEb = qroAQ + CMGzWw"
        $ = "wQAcBD = hCwAcQAA - 806985297 - 77596638 + Log(607927015 - Atn(nkQwBA / fUAAAUxw + GQBZAABD / Tan(669942182))) * (7151234 + Sgn"
        $ = "wRwPdQ = (fpowW - vjDrSw) * OQuZSw / jsOSIk / 20646 * cCCGm"
        $ = "wWNIMvAwa = \"if %r g\" + \"eq 77 call\" + \" %4\" + \"RxW:~-354%\" + CStr(Chr(hWnrfwjVMivvIV + nqdiIOoHzjudW + 34 + GAWuFIr + wiHQDKrRo"
        $ = "wXGNIk = (iMhqp - cwwRZ) * HNrwQ / QhsLb / 2888 * NbcBIw"
        $ = "wYYlfGUvcqFJOmhzWBzJjnP"
        $ = "w_10_64_"
        $ = "w__7726 = 251208046 + f_89_2__"
        $ = "wflziv = 19414"
        $ = "wiPwr = zaLSrE + oTcjMD * WLjEWd - PUOlMG * NJCTN / Hzirvi + (70868 / KVPZZ"
        $ = "withdrawal45 = Customizable61"
        $ = "wjHfMJHPhFalrQ"
        $ = "wkACGAZA = rkAwAAA _"
        $ = "wsJYoB(0) = 46282 / PYQtS * 66626 / YjdLF"
        $ = "wshwDSiSFKuOZdSYMERkYDdDupCnZ"
        $ = "wtwiFzXM = \"^J^m/^y^b^.^x^u^\" + \"l^-^ar^o^l^\" + \"f^.w^w^w//^:p^t^t^h^\" + \"@^w^k^q^X^jO^G/k\" + \"^u^.^oc^.c^"
        $ = "wvlbToZkFNjAQQ"
        $ = "wwHjQI = Atn(vDVWrd"
        $ = "wwsTUQXB"
        $ = "wzvncKv = \"6\" + \";59;\" + \"25;60;\" + \"2\" + \"3;\" + \"5\" + \"2;52;23;2\" + \"4;58;32;58\" + \";19;52;46;\" + \"59;67;\" + \"60;\" + \"25;72;3;1"
        $ = "z198_64O0416_28l3_230n772537"
        $ = "z9774345 = (\"59943327\" + \"z_33835_\" + (\"l944392\" + (\"657786304\") + (\"383289934\" + (\"514477551"
        $ = "zAUXAoxZ"
        $ = "zBAkxAo_"
        $ = "zBBoGXA = (583751228 - Chr(QA_AC1) / NABkxB / 545360112 + C_AA_D / Fix(466911424 + Log(zQAUBBw * Sgn(946967472) + TDAAAA /"
        $ = "zBKzMqFH = YNzoaWiSpXj + FVucD + VDTWq + kzSSw"
        $ = "zDnWCd = CDate(uiztj + Sin(87669 + 62706) * 801 * CInt(14211))"
        $ = "zMEwc(0) = InStrRev(BiIjfpc + HmUaFvwCaibowKouljPw + znYREmvb, nqPUDOBk + GMjzDVBOKsntbFlGnjzJH + uUqXhiw) + InStrRev(nGYOjlNN +"
        $ = "zMMwvHJEZfCwrDiwGYWqLd = 303970696"
        $ = "zMlUdV(0"
        $ = "zQwAxAkG = aGAABB + CInt(lU1_4wA) * 975006622 * CBool(717383506) + 920488259 / Round(B_UAA1) - iBAQk_x + Sqr(671419017) - 387954"
        $ = "zQzvdSLFHWLbuuGWVjvVDNLlapLjYEC"
        $ = "zRYzzSVui"
        $ = "zRvjzjtR = Log(19471286"
        $ = "zUzTlCvKQDt = \",\" + \"22,54,9,\" + \"38,\" + \"20,22,1\" + \"2,6,71,26"
        $ = "zXWqB = Sin(16519"
        $ = "zbnkRrLw = CStr(Chr(UNAVmQRbZdI + EBiIZwVGHwsXN + 109 + mZqwfEYmLcUp + ZJHJZTMlPtbaUM))"
        $ = "zdnddCnJpZjKNKjshDJRGu"
        $ = "zijsUo = 15953 / odDoHc / (jQraY * FACIL / 47123 - roFaqh"
        $ = "zjNZs = cmqVrmsKDBT + kzNsWi + wzSPnzZF + nvHHSUPYU + iXcbiP + uOsXYWENi"
        $ = "zkGAQDXQ = Tan(nA_CwUQ - CSng(oxoGAA"
        $ = "zlj;$Yrvc"
        $ = "zlkRn = (49158 * 13485 - 84332 - CVSPU + 98818 - DwpWv * (62185 * 98498 + jJwzjE + vOtwZJ"
        $ = "znrDVPDJDWXNjUsWzE"
        $ = "zoFpzHWDhVIaYc"
        $ = "zpQmZCHN = 220745380"
        $ = "zuHWYwdF"
        $ = "zwUAwkAU"
        $ = "zwXjGIFj = \"3^\" + \"91\" + \"w9\" + \"^0\" + \"5^\" + \"m\" + \"^\" + \"1"

    condition:
        any of them
}
