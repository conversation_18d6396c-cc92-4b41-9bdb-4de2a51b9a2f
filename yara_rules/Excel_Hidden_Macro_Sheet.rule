// see the relevant post at: http://blog.inquest.net/blog/2019/01/29/Carving-Sneaky-XLM-Files/
rule Excel_Hidden_Macro_Sheet
{
    meta:
        Author      = "InQuest Labs"
        URL         = "https://github.com/InQuest/yara-rules"
        Description = "http://blog.inquest.net/blog/2019/01/29/Carving-Sneaky-XLM-Files/"
    strings:
            $ole_marker     = {D0 CF 11 E0 A1 B1 1A E1}
            $macro_sheet_h1 = {85 00 ?? ?? ?? ?? ?? ?? 01 01}
            $macro_sheet_h2 = {85 00 ?? ?? ?? ?? ?? ?? 02 01}
    condition:
            $ole_marker at 0 and 1 of ($macro_sheet_h*)
}
