<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>Untitled</title>
  <style type="text/css">
      code{white-space: pre-wrap;}
      span.smallcaps{font-variant: small-caps;}
      span.underline{text-decoration: underline;}
      div.column{display: inline-block; vertical-align: top; width: 50%;}
  </style>
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<h1 id="pyxswf">pyxswf</h1>
<p>pyxswf is a script to detect, extract and analyze Flash objects (SWF files) that may be embedded in files such as MS Office documents (e.g. Word, Excel), which is especially useful for malware analysis.</p>
<p>It is part of the <a href="http://www.decalage.info/python/oletools">python-oletools</a> package.</p>
<p>pyxswf is an extension to <a href="http://hooked-on-mnemonics.blogspot.nl/2011/12/xxxswfpy.html">xxxswf.py</a> published by Alexander Hanel.</p>
<p>Compared to xxxswf, it can extract streams from MS Office documents by parsing their OLE structure properly, which is necessary when streams are fragmented. Stream fragmentation is a known obfuscation technique, as explained on <a href="http://web.archive.org/web/20121118021207/http://www.breakingpointsystems.com/resources/blog/evasion-with-ole2-fragmentation/">http://www.breakingpointsystems.com/resources/blog/evasion-with-ole2-fragmentation/</a></p>
<p>It can also extract Flash objects from RTF documents, by parsing embedded objects encoded in hexadecimal format (-f option).</p>
<p>For this, simply add the -o option to work on OLE streams rather than raw files, or the -f option to work on RTF files.</p>
<h2 id="usage">Usage</h2>
<pre class="text"><code>Usage: pyxswf [options] &lt;file.bad&gt;

Options:
  -o, --ole             Parse an OLE file (e.g. Word, Excel) to look for SWF
                        in each stream
  -f, --rtf             Parse an RTF file to look for SWF in each embedded
                        object
  -x, --extract         Extracts the embedded SWF(s), names it MD5HASH.swf &amp;
                        saves it in the working dir. No addition args needed
  -h, --help            show this help message and exit
  -y, --yara            Scans the SWF(s) with yara. If the SWF(s) is
                        compressed it will be deflated. No addition args
                        needed
  -s, --md5scan         Scans the SWF(s) for MD5 signatures. Please see func
                        checkMD5 to define hashes. No addition args needed
  -H, --header          Displays the SWFs file header. No addition args needed
  -d, --decompress      Deflates compressed SWFS(s)
  -r PATH, --recdir=PATH
                        Will recursively scan a directory for files that
                        contain SWFs. Must provide path in quotes
  -c, --compress        Compresses the SWF using Zlib</code></pre>
<h3 id="example-1---detecting-and-extracting-a-swf-file-from-a-word-document-on-windows">Example 1 - detecting and extracting a SWF file from a Word document on Windows:</h3>
<pre class="text"><code>C:\oletools&gt;pyxswf -o word_flash.doc
OLE stream: &#39;Contents&#39;
[SUMMARY] 1 SWF(s) in MD5:993664cc86f60d52d671b6610813cfd1:Contents
        [ADDR] SWF 1 at 0x8  - FWS Header

C:\oletools&gt;pyxswf -xo word_flash.doc
OLE stream: &#39;Contents&#39;
[SUMMARY] 1 SWF(s) in MD5:993664cc86f60d52d671b6610813cfd1:Contents
        [ADDR] SWF 1 at 0x8  - FWS Header
                [FILE] Carved SWF MD5: 2498e9c0701dc0e461ab4358f9102bc5.swf</code></pre>
<h3 id="example-2---detecting-and-extracting-a-swf-file-from-a-rtf-document-on-windows">Example 2 - detecting and extracting a SWF file from a RTF document on Windows:</h3>
<pre class="text"><code>C:\oletools&gt;pyxswf -xf &quot;rtf_flash.rtf&quot;
RTF embedded object size 1498557 at index 000036DD
[SUMMARY] 1 SWF(s) in MD5:46a110548007e04f4043785ac4184558:RTF_embedded_object_0
00036DD
        [ADDR] SWF 1 at 0xc40  - FWS Header
                [FILE] Carved SWF MD5: 2498e9c0701dc0e461ab4358f9102bc5.swf</code></pre>
<h2 id="how-to-use-pyxswf-in-python-applications">How to use pyxswf in Python applications</h2>
<p>TODO</p>
<hr />
<h2 id="python-oletools-documentation">python-oletools documentation</h2>
<ul>
<li><a href="Home.html">Home</a></li>
<li><a href="License.html">License</a></li>
<li><a href="Install.html">Install</a></li>
<li><a href="Contribute.html">Contribute</a>, Suggest Improvements or Report Issues</li>
<li>Tools:
<ul>
<li><a href="mraptor.html">mraptor</a></li>
<li><a href="msodde.html">msodde</a></li>
<li><a href="olebrowse.html">olebrowse</a></li>
<li><a href="oledir.html">oledir</a></li>
<li><a href="oleid.html">oleid</a></li>
<li><a href="olemap.html">olemap</a></li>
<li><a href="olemeta.html">olemeta</a></li>
<li><a href="oleobj.html">oleobj</a></li>
<li><a href="oletimes.html">oletimes</a></li>
<li><a href="olevba.html">olevba</a></li>
<li><a href="pyxswf.html">pyxswf</a></li>
<li><a href="rtfobj.html">rtfobj</a></li>
</ul></li>
</ul>
</body>
</html>
