<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>Untitled</title>
  <style type="text/css">
      code{white-space: pre-wrap;}
      span.smallcaps{font-variant: small-caps;}
      span.underline{text-decoration: underline;}
      div.column{display: inline-block; vertical-align: top; width: 50%;}
  </style>
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<h1 id="how-to-suggest-improvements-report-issues-or-contribute">How to Suggest Improvements, Report Issues or Contribute</h1>
<p>This is a personal open-source project, developed on my spare time. Any contribution, suggestion, feedback or bug report is welcome.</p>
<p>To <strong>suggest improvements, report a bug or any issue</strong>, please use the <a href="https://github.com/decalage2/oletools/issues">issue reporting page</a>, and provide all the information and files to reproduce the problem.</p>
<p>You may also <a href="http://decalage.info/contact">contact the author</a> directly to <strong>send feedback</strong>.</p>
<p>The code is available in <a href="https://github.com/decalage2/oletools">a repository on GitHub</a>. You may use it to <strong>submit enhancements</strong> using forks and pull requests.</p>
<hr />
<h2 id="python-oletools-documentation">python-oletools documentation</h2>
<ul>
<li><a href="Home.html">Home</a></li>
<li><a href="License.html">License</a></li>
<li><a href="Install.html">Install</a></li>
<li><a href="Contribute.html">Contribute</a>, Suggest Improvements or Report Issues</li>
<li>Tools:
<ul>
<li><a href="mraptor.html">mraptor</a></li>
<li><a href="msodde.html">msodde</a></li>
<li><a href="olebrowse.html">olebrowse</a></li>
<li><a href="oledir.html">oledir</a></li>
<li><a href="oleid.html">oleid</a></li>
<li><a href="olemap.html">olemap</a></li>
<li><a href="olemeta.html">olemeta</a></li>
<li><a href="oleobj.html">oleobj</a></li>
<li><a href="oletimes.html">oletimes</a></li>
<li><a href="olevba.html">olevba</a></li>
<li><a href="pyxswf.html">pyxswf</a></li>
<li><a href="rtfobj.html">rtfobj</a></li>
</ul></li>
</ul>
</body>
</html>
