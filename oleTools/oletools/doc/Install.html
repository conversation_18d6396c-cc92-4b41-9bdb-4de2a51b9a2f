<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>Untitled</title>
  <style type="text/css">
      code{white-space: pre-wrap;}
      span.smallcaps{font-variant: small-caps;}
      span.underline{text-decoration: underline;}
      div.column{display: inline-block; vertical-align: top; width: 50%;}
  </style>
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<h1 id="how-to-download-and-install-oletools">How to Download and Install oletools</h1>
<h2 id="pre-requisites">Pre-requisites</h2>
<p>The recommended Python version to run oletools is the latest <strong>Python 3.x</strong> (3.9 for now). Python 2.7 is still supported for the moment, even if it reached end of life in 2020 (for projects still using Python 2/PyPy 2 such as ViperMonkey). It is highly recommended to switch to Python 3 if possible.</p>
<h2 id="recommended-way-to-downloadinstallupdate-oletools-pip">Recommended way to Download+Install/Update oletools: pip</h2>
<p>Pip is included with Python since version 2.7.9 and 3.4. If it is not installed on your system, either upgrade Python or see https://pip.pypa.io/en/stable/installing/</p>
<h3 id="linux-mac-osx-unix">Linux, Mac OSX, Unix</h3>
<p>To download and install/update the latest release version of oletools with all its dependencies, run the following command in a shell:</p>
<pre class="text"><code>sudo -H pip install -U oletools[full]</code></pre>
<p>The keyword <code>[full]</code> means that all optional dependencies will be installed, such as XLMMacroDeobfuscator. If you prefer a lighter version without optional dependencies, use the following command instead:</p>
<pre class="text"><code>sudo -H pip install -U oletools</code></pre>
<p>Replace <code>pip</code> by <code>pip3</code> or <code>pip2</code> to install on a specific Python version.</p>
<p><strong>Important</strong>: Since version 0.50, pip will automatically create convenient command-line scripts in /usr/local/bin to run all the oletools from any directory.</p>
<h3 id="windows">Windows</h3>
<p>To download and install/update the latest release version of oletools with all its dependencies, run the following command in a cmd window:</p>
<pre class="text"><code>pip install -U oletools[full]</code></pre>
<p>The keyword <code>[full]</code> means that all optional dependencies will be installed, such as XLMMacroDeobfuscator. If you prefer a lighter version without optional dependencies, use the following command instead:</p>
<pre class="text"><code>pip install -U oletools</code></pre>
<p>Replace <code>pip</code> by <code>pip3</code> or <code>pip2</code> to install on a specific Python version.</p>
<p><strong>Note</strong>: with Python 3, you may need to open a cmd window with Administrator privileges in order to run pip and install for all users. If that is not possible, you may also install only for the current user by adding the <code>--user</code> option:</p>
<pre class="text"><code>pip3 install -U --user oletools</code></pre>
<p><strong>Important</strong>: Since version 0.50, pip will automatically create convenient command-line scripts to run all the oletools from any directory: olevba, mraptor, oleid, rtfobj, etc.</p>
<h2 id="how-to-install-the-latest-development-version">How to install the latest development version</h2>
<p>If you want to benefit from the latest improvements in the development version, you may also use pip:</p>
<h3 id="linux-mac-osx-unix-1">Linux, Mac OSX, Unix</h3>
<pre class="text"><code>sudo -H pip install -U https://github.com/decalage2/oletools/archive/master.zip</code></pre>
<p>Note that it will install oletools without optional dependencies such as XLMMacroDeobfuscator, so you may need to install them separately.</p>
<p>Replace <code>pip</code> by <code>pip3</code> or <code>pip2</code> to install on a specific Python version.</p>
<h3 id="windows-1">Windows</h3>
<pre class="text"><code>pip install -U https://github.com/decalage2/oletools/archive/master.zip</code></pre>
<p>Note that it will install oletools without optional dependencies such as XLMMacroDeobfuscator, so you may need to install them separately.</p>
<p>Replace <code>pip</code> by <code>pip3</code> or <code>pip2</code> to install on a specific Python version.</p>
<p><strong>Note</strong>: with Python 3, you may need to open a cmd window with Administrator privileges in order to run pip and install for all users. If that is not possible, you may also install only for the current user by adding the <code>--user</code> option:</p>
<pre class="text"><code>pip3 install -U --user https://github.com/decalage2/oletools/archive/master.zip</code></pre>
<h2 id="how-to-install-offline---computer-without-internet-access">How to install offline - Computer without Internet access</h2>
<p>First, download the oletools archive on a computer with Internet access: * Latest stable version: from https://pypi.org/project/oletools/ or https://github.com/decalage2/oletools/releases * Development version: https://github.com/decalage2/oletools/archive/master.zip</p>
<p>Copy the archive file to the target computer.</p>
<p>On Linux, Mac OSX, Unix, run the following command using the filename of the archive that you downloaded:</p>
<pre class="text"><code>sudo -H pip install -U oletools.zip</code></pre>
<p>On Windows:</p>
<pre class="text"><code>pip install -U oletools.zip</code></pre>
<h2 id="old-school-install-using-setup.py">Old school install using setup.py</h2>
<p>If you cannot use pip, it is still possible to run the setup.py script directly. However, this method will not create the command-line scripts automatically.</p>
<p>First, download the oletools archive: * Latest stable version: from https://github.com/decalage2/oletools/releases * Development version: https://github.com/decalage2/oletools/archive/master.zip</p>
<p>Then extract the archive, open a shell and go to the oletools directory.</p>
<h3 id="linux-mac-osx-unix-2">Linux, Mac OSX, Unix</h3>
<pre class="text"><code>sudo -H python setup.py install</code></pre>
<h3 id="windows-2">Windows:</h3>
<pre class="text"><code>python setup.py install</code></pre>
<hr />
<h2 id="python-oletools-documentation">python-oletools documentation</h2>
<ul>
<li><a href="Home.html">Home</a></li>
<li><a href="License.html">License</a></li>
<li><a href="Install.html">Install</a></li>
<li><a href="Contribute.html">Contribute</a>, Suggest Improvements or Report Issues</li>
<li>Tools:
<ul>
<li><a href="mraptor.html">mraptor</a></li>
<li><a href="msodde.html">msodde</a></li>
<li><a href="olebrowse.html">olebrowse</a></li>
<li><a href="oledir.html">oledir</a></li>
<li><a href="oleid.html">oleid</a></li>
<li><a href="olemap.html">olemap</a></li>
<li><a href="olemeta.html">olemeta</a></li>
<li><a href="oleobj.html">oleobj</a></li>
<li><a href="oletimes.html">oletimes</a></li>
<li><a href="olevba.html">olevba</a></li>
<li><a href="pyxswf.html">pyxswf</a></li>
<li><a href="rtfobj.html">rtfobj</a></li>
</ul></li>
</ul>
</body>
</html>
