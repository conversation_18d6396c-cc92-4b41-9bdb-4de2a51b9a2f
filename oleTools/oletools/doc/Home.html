<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>Untitled</title>
  <style type="text/css">
      code{white-space: pre-wrap;}
      span.smallcaps{font-variant: small-caps;}
      span.underline{text-decoration: underline;}
      div.column{display: inline-block; vertical-align: top; width: 50%;}
  </style>
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<h1 id="python-oletools-v0.56-documentation">python-oletools v0.56 documentation</h1>
<p>This is the home page of the documentation for python-oletools. The latest version can be found <a href="https://github.com/decalage2/oletools/wiki">online</a>, otherwise a copy is provided in the doc subfolder of the package.</p>
<p><a href="http://www.decalage.info/python/oletools">python-oletools</a> is a package of python tools to analyze <a href="http://en.wikipedia.org/wiki/Compound_File_Binary_Format">Microsoft OLE2 files</a> (also called Structured Storage, Compound File Binary Format or Compound Document File Format), such as Microsoft Office documents or Outlook messages, mainly for malware analysis, forensics and debugging. It is based on the <a href="http://www.decalage.info/olefile">olefile</a> parser. See <a href="http://www.decalage.info/python/oletools" class="uri">http://www.decalage.info/python/oletools</a> for more info.</p>
<p><strong>Quick links:</strong> <a href="http://www.decalage.info/python/oletools">Home page</a> - <a href="https://github.com/decalage2/oletools/wiki/Install">Download/Install</a> - <a href="https://github.com/decalage2/oletools/wiki">Documentation</a> - <a href="https://github.com/decalage2/oletools/issues">Report Issues/Suggestions/Questions</a> - <a href="http://decalage.info/contact">Contact the Author</a> - <a href="https://github.com/decalage2/oletools">Repository</a> - <a href="https://twitter.com/decalage2">Updates on Twitter</a></p>
<p>Note: python-oletools is not related to OLETools published by BeCubed Software.</p>
<h2 id="tools-in-python-oletools">Tools in python-oletools:</h2>
<h3 id="tools-to-analyze-malicious-documents">Tools to analyze malicious documents</h3>
<ul>
<li><strong><a href="oleid.html">oleid</a></strong>: to analyze OLE files to detect specific characteristics usually found in malicious files.</li>
<li><strong><a href="olevba.html">olevba</a></strong>: to extract and analyze VBA Macro source code from MS Office documents (OLE and OpenXML).</li>
<li><strong><a href="mraptor.html">mraptor</a></strong>: to detect malicious VBA Macros</li>
<li><strong><a href="msodde.html">msodde</a></strong>: to detect and extract DDE/DDEAUTO links from MS Office documents, RTF and CSV</li>
<li><strong><a href="pyxswf.html">pyxswf</a></strong>: to detect, extract and analyze Flash objects (SWF) that may be embedded in files such as MS Office documents (e.g. Word, Excel) and RTF, which is especially useful for malware analysis.</li>
<li><strong><a href="oleobj.html">oleobj</a></strong>: to extract embedded objects from OLE files.</li>
<li><strong><a href="rtfobj.html">rtfobj</a></strong>: to extract embedded objects from RTF files.</li>
</ul>
<h3 id="tools-to-analyze-the-structure-of-ole-files">Tools to analyze the structure of OLE files</h3>
<ul>
<li><strong><a href="olebrowse.html">olebrowse</a></strong>: A simple GUI to browse OLE files (e.g. MS Word, Excel, Powerpoint documents), to view and extract individual data streams.</li>
<li><strong><a href="olemeta.html">olemeta</a></strong>: to extract all standard properties (metadata) from OLE files.</li>
<li><strong><a href="oletimes.html">oletimes</a></strong>: to extract creation and modification timestamps of all streams and storages.</li>
<li><strong><a href="oledir.html">oledir</a></strong>: to display all the directory entries of an OLE file, including free and orphaned entries.</li>
<li><strong><a href="olemap.html">olemap</a></strong>: to display a map of all the sectors in an OLE file.</li>
<li>and a few others (coming soon)</li>
</ul>
<hr />
<h2 id="python-oletools-documentation">python-oletools documentation</h2>
<ul>
<li><a href="Home.html">Home</a></li>
<li><a href="License.html">License</a></li>
<li><a href="Install.html">Install</a></li>
<li><a href="Contribute.html">Contribute</a>, Suggest Improvements or Report Issues</li>
<li>Tools:
<ul>
<li><a href="mraptor.html">mraptor</a></li>
<li><a href="msodde.html">msodde</a></li>
<li><a href="olebrowse.html">olebrowse</a></li>
<li><a href="oledir.html">oledir</a></li>
<li><a href="oleid.html">oleid</a></li>
<li><a href="olemap.html">olemap</a></li>
<li><a href="olemeta.html">olemeta</a></li>
<li><a href="oleobj.html">oleobj</a></li>
<li><a href="oletimes.html">oletimes</a></li>
<li><a href="olevba.html">olevba</a></li>
<li><a href="pyxswf.html">pyxswf</a></li>
<li><a href="rtfobj.html">rtfobj</a></li>
</ul></li>
</ul>
</body>
</html>
