<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>Untitled</title>
  <style type="text/css">
      code{white-space: pre-wrap;}
      span.smallcaps{font-variant: small-caps;}
      span.underline{text-decoration: underline;}
      div.column{display: inline-block; vertical-align: top; width: 50%;}
  </style>
  <style type="text/css">
a.sourceLine { display: inline-block; line-height: 1.25; }
a.sourceLine { pointer-events: none; color: inherit; text-decoration: inherit; }
a.sourceLine:empty { height: 1.2em; position: absolute; }
.sourceCode { overflow: visible; }
code.sourceCode { white-space: pre; position: relative; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
code.sourceCode { white-space: pre-wrap; }
a.sourceLine { text-indent: -1em; padding-left: 1em; }
}
pre.numberSource a.sourceLine
  { position: relative; }
pre.numberSource a.sourceLine:empty
  { position: absolute; }
pre.numberSource a.sourceLine::before
  { content: attr(data-line-number);
    position: absolute; left: -5em; text-align: right; vertical-align: baseline;
    border: none; pointer-events: all;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
    color: #aaaaaa;
  }
pre.numberSource { margin-left: 3em; border-left: 1px solid #aaaaaa;  padding-left: 4px; }
div.sourceCode
  {  }
@media screen {
a.sourceLine::before { text-decoration: underline; }
}
code span.al { color: #ff0000; font-weight: bold; } /* Alert */
code span.an { color: #60a0b0; font-weight: bold; font-style: italic; } /* Annotation */
code span.at { color: #7d9029; } /* Attribute */
code span.bn { color: #40a070; } /* BaseN */
code span.bu { } /* BuiltIn */
code span.cf { color: #007020; font-weight: bold; } /* ControlFlow */
code span.ch { color: #4070a0; } /* Char */
code span.cn { color: #880000; } /* Constant */
code span.co { color: #60a0b0; font-style: italic; } /* Comment */
code span.cv { color: #60a0b0; font-weight: bold; font-style: italic; } /* CommentVar */
code span.do { color: #ba2121; font-style: italic; } /* Documentation */
code span.dt { color: #902000; } /* DataType */
code span.dv { color: #40a070; } /* DecVal */
code span.er { color: #ff0000; font-weight: bold; } /* Error */
code span.ex { } /* Extension */
code span.fl { color: #40a070; } /* Float */
code span.fu { color: #06287e; } /* Function */
code span.im { } /* Import */
code span.in { color: #60a0b0; font-weight: bold; font-style: italic; } /* Information */
code span.kw { color: #007020; font-weight: bold; } /* Keyword */
code span.op { color: #666666; } /* Operator */
code span.ot { color: #007020; } /* Other */
code span.pp { color: #bc7a00; } /* Preprocessor */
code span.sc { color: #4070a0; } /* SpecialChar */
code span.ss { color: #bb6688; } /* SpecialString */
code span.st { color: #4070a0; } /* String */
code span.va { color: #19177c; } /* Variable */
code span.vs { color: #4070a0; } /* VerbatimString */
code span.wa { color: #60a0b0; font-weight: bold; font-style: italic; } /* Warning */
  </style>
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<h1 id="rtfobj">rtfobj</h1>
<p>rtfobj is a Python module to detect and extract embedded objects stored in RTF files, such as OLE objects. It can also detect OLE Package objects, and extract the embedded files.</p>
<p>Since v0.50, rtfobj contains a custom RTF parser that has been designed to match MS Word’s behaviour, in order to handle obfuscated RTF files. See my article <a href="http://decalage.info/rtf_tricks">“Anti-Analysis Tricks in Weaponized RTF”</a> for some concrete examples.</p>
<p>rtfobj can be used as a Python library or a command-line tool.</p>
<p>It is part of the <a href="http://www.decalage.info/python/oletools">python-oletools</a> package.</p>
<h2 id="usage">Usage</h2>
<pre class="text"><code>rtfobj [options] &lt;filename&gt; [filename2 ...]

Options:
  -h, --help            show this help message and exit
  -r                    find files recursively in subdirectories.
  -z ZIP_PASSWORD, --zip=ZIP_PASSWORD
                        if the file is a zip archive, open first file from it,
                        using the provided password (requires Python 2.6+)
  -f ZIP_FNAME, --zipfname=ZIP_FNAME
                        if the file is a zip archive, file(s) to be opened
                        within the zip. Wildcards * and ? are supported.
                        (default:*)
  -l LOGLEVEL, --loglevel=LOGLEVEL
                        logging level debug/info/warning/error/critical
                        (default=warning)
  -s SAVE_OBJECT, --save=SAVE_OBJECT
                        Save the object corresponding to the provided number
                        to a file, for example &quot;-s 2&quot;. Use &quot;-s all&quot; to save
                        all objects at once.
  -d OUTPUT_DIR         use specified directory to save output files.</code></pre>
<p>rtfobj displays a list of the OLE and Package objects that have been detected, with their attributes such as class and filename.</p>
<p>When an OLE Package object contains an executable file or script, it is highlighted as such. For example:</p>
<p><img src="rtfobj1.png" /></p>
<p>To extract an object or file, use the option -s followed by the object number as shown in the table.</p>
<p>Example:</p>
<pre class="text"><code>rtfobj -s 0</code></pre>
<p>It extracts and decodes the corresponding object, and saves it as a file named “object_xxxx.bin”, xxxx being the location of the object in the RTF file.</p>
<h2 id="how-to-use-rtfobj-in-python-applications">How to use rtfobj in Python applications</h2>
<p>As of v0.50, the API has changed significantly and it is not final yet. For now, see the class RtfObjectParser in the code.</p>
<h3 id="deprecated-api-still-functional">Deprecated API (still functional):</h3>
<p>rtf_iter_objects(filename) is an iterator which yields a tuple (index, orig_len, object) providing the index of each hexadecimal stream in the RTF file, and the corresponding decoded object.</p>
<p>Example:</p>
<div class="sourceCode" id="cb3"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb3-1" data-line-number="1"><span class="im">from</span> oletools <span class="im">import</span> rtfobj</a>
<a class="sourceLine" id="cb3-2" data-line-number="2"><span class="cf">for</span> index, orig_len, data <span class="kw">in</span> rtfobj.rtf_iter_objects(<span class="st">&quot;myfile.rtf&quot;</span>):</a>
<a class="sourceLine" id="cb3-3" data-line-number="3">    <span class="bu">print</span>(<span class="st">&#39;found object size </span><span class="sc">%d</span><span class="st"> at index </span><span class="sc">%08X</span><span class="st">&#39;</span> <span class="op">%</span> (<span class="bu">len</span>(data), index))</a></code></pre></div>
<hr />
<h2 id="python-oletools-documentation">python-oletools documentation</h2>
<ul>
<li><a href="Home.html">Home</a></li>
<li><a href="License.html">License</a></li>
<li><a href="Install.html">Install</a></li>
<li><a href="Contribute.html">Contribute</a>, Suggest Improvements or Report Issues</li>
<li>Tools:
<ul>
<li><a href="mraptor.html">mraptor</a></li>
<li><a href="msodde.html">msodde</a></li>
<li><a href="olebrowse.html">olebrowse</a></li>
<li><a href="oledir.html">oledir</a></li>
<li><a href="oleid.html">oleid</a></li>
<li><a href="olemap.html">olemap</a></li>
<li><a href="olemeta.html">olemeta</a></li>
<li><a href="oleobj.html">oleobj</a></li>
<li><a href="oletimes.html">oletimes</a></li>
<li><a href="olevba.html">olevba</a></li>
<li><a href="pyxswf.html">pyxswf</a></li>
<li><a href="rtfobj.html">rtfobj</a></li>
</ul></li>
</ul>
</body>
</html>
