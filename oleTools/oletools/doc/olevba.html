<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>Untitled</title>
  <style type="text/css">
      code{white-space: pre-wrap;}
      span.smallcaps{font-variant: small-caps;}
      span.underline{text-decoration: underline;}
      div.column{display: inline-block; vertical-align: top; width: 50%;}
  </style>
  <style type="text/css">
a.sourceLine { display: inline-block; line-height: 1.25; }
a.sourceLine { pointer-events: none; color: inherit; text-decoration: inherit; }
a.sourceLine:empty { height: 1.2em; position: absolute; }
.sourceCode { overflow: visible; }
code.sourceCode { white-space: pre; position: relative; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
code.sourceCode { white-space: pre-wrap; }
a.sourceLine { text-indent: -1em; padding-left: 1em; }
}
pre.numberSource a.sourceLine
  { position: relative; }
pre.numberSource a.sourceLine:empty
  { position: absolute; }
pre.numberSource a.sourceLine::before
  { content: attr(data-line-number);
    position: absolute; left: -5em; text-align: right; vertical-align: baseline;
    border: none; pointer-events: all;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
    color: #aaaaaa;
  }
pre.numberSource { margin-left: 3em; border-left: 1px solid #aaaaaa;  padding-left: 4px; }
div.sourceCode
  {  }
@media screen {
a.sourceLine::before { text-decoration: underline; }
}
code span.al { color: #ff0000; font-weight: bold; } /* Alert */
code span.an { color: #60a0b0; font-weight: bold; font-style: italic; } /* Annotation */
code span.at { color: #7d9029; } /* Attribute */
code span.bn { color: #40a070; } /* BaseN */
code span.bu { } /* BuiltIn */
code span.cf { color: #007020; font-weight: bold; } /* ControlFlow */
code span.ch { color: #4070a0; } /* Char */
code span.cn { color: #880000; } /* Constant */
code span.co { color: #60a0b0; font-style: italic; } /* Comment */
code span.cv { color: #60a0b0; font-weight: bold; font-style: italic; } /* CommentVar */
code span.do { color: #ba2121; font-style: italic; } /* Documentation */
code span.dt { color: #902000; } /* DataType */
code span.dv { color: #40a070; } /* DecVal */
code span.er { color: #ff0000; font-weight: bold; } /* Error */
code span.ex { } /* Extension */
code span.fl { color: #40a070; } /* Float */
code span.fu { color: #06287e; } /* Function */
code span.im { } /* Import */
code span.in { color: #60a0b0; font-weight: bold; font-style: italic; } /* Information */
code span.kw { color: #007020; font-weight: bold; } /* Keyword */
code span.op { color: #666666; } /* Operator */
code span.ot { color: #007020; } /* Other */
code span.pp { color: #bc7a00; } /* Preprocessor */
code span.sc { color: #4070a0; } /* SpecialChar */
code span.ss { color: #bb6688; } /* SpecialString */
code span.st { color: #4070a0; } /* String */
code span.va { color: #19177c; } /* Variable */
code span.vs { color: #4070a0; } /* VerbatimString */
code span.wa { color: #60a0b0; font-weight: bold; font-style: italic; } /* Warning */
  </style>
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<h1 id="olevba">olevba</h1>
<p>olevba is a script to parse OLE and OpenXML files such as MS Office documents (e.g. Word, Excel), to <strong>detect VBA Macros</strong>, extract their <strong>source code</strong> in clear text, and detect security-related patterns such as <strong>auto-executable macros</strong>, <strong>suspicious VBA keywords</strong> used by malware, anti-sandboxing and anti-virtualization techniques, and potential <strong>IOCs</strong> (IP addresses, URLs, executable filenames, etc). It also detects and decodes several common <strong>obfuscation methods including Hex encoding, StrReverse, Base64, Dridex, VBA expressions</strong>, and extracts IOCs from decoded strings. XLM/Excel 4 Macros are also supported in Excel and SLK files.</p>
<p>It can be used either as a command-line tool, or as a python module from your own applications.</p>
<p>It is part of the <a href="http://www.decalage.info/python/oletools">python-oletools</a> package.</p>
<p>olevba is based on source code from <a href="https://github.com/unixfreak0037/officeparser">officeparser</a> by John William Davison, with significant modifications.</p>
<h2 id="supported-formats">Supported formats</h2>
<ul>
<li>Word 97-2003 (.doc, .dot), Word 2007+ (.docm, .dotm)</li>
<li>Excel 97-2003 (.xls), Excel 2007+ (.xlsm, .xlsb)</li>
<li>PowerPoint 97-2003 (.ppt), PowerPoint 2007+ (.pptm, .ppsm)</li>
<li>Word/PowerPoint 2007+ XML (aka Flat OPC)</li>
<li>Word 2003 XML (.xml)</li>
<li>Word/Excel Single File Web Page / MHTML (.mht)</li>
<li>Publisher (.pub)</li>
<li>SYLK/SLK files (.slk)</li>
<li>Text file containing VBA or VBScript source code</li>
<li>Password-protected Zip archive containing any of the above</li>
</ul>
<p>S## Main Features</p>
<ul>
<li>Detect VBA macros in MS Office 97-2003 and 2007+ files, XML, MHT</li>
<li>Extract VBA macro source code</li>
<li>Detect auto-executable macros</li>
<li>Detect suspicious VBA keywords often used by malware</li>
<li>Detect anti-sandboxing and anti-virtualization techniques</li>
<li>Detect and decodes strings obfuscated with Hex/Base64/StrReverse/Dridex</li>
<li>Deobfuscates VBA expressions with any combination of Chr, Asc, Val, StrReverse, Environ, +, &amp;, using a VBA parser built with <a href="http://pyparsing.wikispaces.com">pyparsing</a>, including custom Hex and Base64 encodings</li>
<li>Extract IOCs/patterns of interest such as IP addresses, URLs, e-mail addresses and executable file names</li>
<li>Scan multiple files and sample collections (wildcards, recursive)</li>
<li>Triage mode for a summary view of multiple files</li>
<li>Scan malware samples in password-protected Zip archives</li>
<li>Python API to use olevba from your applications</li>
</ul>
<p>MS Office files encrypted with a password are also supported, because VBA macro code is never encrypted, only the content of the document.</p>
<h2 id="about-vba-macros">About VBA Macros</h2>
<p>See <a href="http://www.decalage.info/en/vba_tools">this article</a> for more information and technical details about VBA Macros and how they are stored in MS Office documents.</p>
<h2 id="how-it-works">How it works</h2>
<ol type="1">
<li>olevba checks the file type: If it is an OLE file (i.e MS Office 97-2003), it is parsed right away.</li>
<li>If it is a zip file (i.e. MS Office 2007+), XML or MHTML, olevba looks for all OLE files stored in it (e.g. vbaProject.bin, editdata.mso), and opens them.</li>
<li>olevba identifies all the VBA projects stored in the OLE structure.</li>
<li>Each VBA project is parsed to find the corresponding OLE streams containing macro code.</li>
<li>In each of these OLE streams, the VBA macro source code is extracted and decompressed (RLE compression).</li>
<li>olevba looks for specific strings obfuscated with various algorithms (Hex, Base64, StrReverse, Dridex, VBA expressions).</li>
<li>olevba scans the macro source code and the deobfuscated strings to find suspicious keywords, auto-executable macros and potential IOCs (URLs, IP addresses, e-mail addresses, executable filenames, etc).</li>
</ol>
<h2 id="usage">Usage</h2>
<pre class="text"><code>Usage: olevba [options] &lt;filename&gt; [filename2 ...]

Options:
  -h, --help            show this help message and exit
  -r                    find files recursively in subdirectories.
  -z ZIP_PASSWORD, --zip=ZIP_PASSWORD
                        if the file is a zip archive, open all files from it,
                        using the provided password.
  -p PASSWORD, --password=PASSWORD
                        if encrypted office files are encountered, try
                        decryption with this password. May be repeated.
  -f ZIP_FNAME, --zipfname=ZIP_FNAME
                        if the file is a zip archive, file(s) to be opened
                        within the zip. Wildcards * and ? are supported.
                        (default:*)
  -a, --analysis        display only analysis results, not the macro source
                        code
  -c, --code            display only VBA source code, do not analyze it
  --decode              display all the obfuscated strings with their decoded
                        content (Hex, Base64, StrReverse, Dridex, VBA).
  --attr                display the attribute lines at the beginning of VBA
                        source code
  --reveal              display the macro source code after replacing all the
                        obfuscated strings by their decoded content.
  -l LOGLEVEL, --loglevel=LOGLEVEL
                        logging level debug/info/warning/error/critical
                        (default=warning)
  --deobf               Attempt to deobfuscate VBA expressions (slow)
  --relaxed             Do not raise errors if opening of substream fails

  Output mode (mutually exclusive):
    -t, --triage        triage mode, display results as a summary table
                        (default for multiple files)
    -d, --detailed      detailed mode, display full results (default for
                        single file)
    -j, --json          json mode, detailed in json format (never default)</code></pre>
<p><strong>New in v0.54:</strong> the -p option can now be used to decrypt encrypted documents using the provided password(s).</p>
<h3 id="examples">Examples</h3>
<p>Scan a single file:</p>
<pre class="text"><code>olevba file.doc</code></pre>
<p>Scan a single file, stored in a Zip archive with password “infected”:</p>
<pre class="text"><code>olevba malicious_file.xls.zip -z infected</code></pre>
<p>Scan a single file, showing all obfuscated strings decoded:</p>
<pre class="text"><code>olevba file.doc --decode</code></pre>
<p>Scan a single file, showing the macro source code with VBA strings deobfuscated:</p>
<pre class="text"><code>olevba file.doc --reveal</code></pre>
<p>Scan VBA source code extracted into a text file:</p>
<pre class="text"><code>olevba source_code.vba</code></pre>
<p>Scan a collection of files stored in a folder:</p>
<pre class="text"><code>olevba &quot;MalwareZoo/VBA/*&quot;</code></pre>
<p>NOTE: On Linux, MacOSX and other Unix variants, it is required to add double quotes around wildcards. Otherwise, they will be expanded by the shell instead of olevba.</p>
<p>Scan all .doc and .xls files, recursively in all subfolders:</p>
<pre class="text"><code>olevba &quot;MalwareZoo/VBA/*.doc&quot; &quot;MalwareZoo/VBA/*.xls&quot; -r</code></pre>
<p>Scan all .doc files within all .zip files with password, recursively:</p>
<pre class="text"><code>olevba &quot;MalwareZoo/VBA/*.zip&quot; -r -z infected -f &quot;*.doc&quot;</code></pre>
<h3 id="detailed-analysis-mode-default-for-single-file">Detailed analysis mode (default for single file)</h3>
<p>When a single file is scanned, or when using the option -d, all details of the analysis are displayed.</p>
<p>For example, checking the malware sample <a href="https://malwr.com/analysis/M2I4YWRhM2IwY2QwNDljN2E3ZWFjYTg3ODk4NmZhYmE/">DIAN_caso-5415.doc</a>:</p>
<pre class="text"><code>&gt;olevba c:\MalwareZoo\VBA\DIAN_caso-5415.doc.zip -z infected
===============================================================================
FILE: DIAN_caso-5415.doc.malware in c:\MalwareZoo\VBA\DIAN_caso-5415.doc.zip
Type: OLE
-------------------------------------------------------------------------------
VBA MACRO ThisDocument.cls
in file: DIAN_caso-5415.doc.malware - OLE stream: Macros/VBA/ThisDocument
- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
Option Explicit
Private Declare Function URLDownloadToFileA Lib &quot;urlmon&quot; (ByVal FVQGKS As Long,_
ByVal WSGSGY As String, ByVal IFRRFV As String, ByVal NCVOLV As Long, _
ByVal HQTLDG As Long) As Long
Sub AutoOpen()
    Auto_Open
End Sub
Sub Auto_Open()
SNVJYQ
End Sub
Public Sub SNVJYQ()
    [Malicious Code...]
End Sub
Function OGEXYR(XSTAHU As String, PHHWIV As String) As Boolean
    [Malicious Code...]
    Application.DisplayAlerts = False
    Application.Quit
End Function
Sub Workbook_Open()
    Auto_Open
End Sub

- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
ANALYSIS:
+------------+----------------------+-----------------------------------------+
| Type       | Keyword              | Description                             |
+------------+----------------------+-----------------------------------------+
| AutoExec   | AutoOpen             | Runs when the Word document is opened   |
| AutoExec   | Auto_Open            | Runs when the Excel Workbook is opened  |
| AutoExec   | Workbook_Open        | Runs when the Excel Workbook is opened  |
| Suspicious | Lib                  | May run code from a DLL                 |
| Suspicious | Shell                | May run an executable file or a system  |
|            |                      | command                                 |
| Suspicious | Environ              | May read system environment variables   |
| Suspicious | URLDownloadToFileA   | May download files from the Internet    |
| IOC        | http://germanya.com. | URL                                     |
|            | ec/logs/test.exe&quot;    |                                         |
| IOC        | http://germanya.com. | URL                                     |
|            | ec/logs/counter.php&quot; |                                         |
| IOC        | germanya.com         | Executable file name                    |
| IOC        | test.exe             | Executable file name                    |
| IOC        | sfjozjero.exe        | Executable file name                    |
+------------+----------------------+-----------------------------------------+</code></pre>
<h3 id="triage-mode-default-for-multiple-files">Triage mode (default for multiple files)</h3>
<p>When several files are scanned, or when using the option -t, a summary of the analysis for each file is displayed. This is more convenient for quick triage of a collection of suspicious files.</p>
<p>The following flags show the results of the analysis:</p>
<ul>
<li><strong>OLE</strong>: the file type is OLE, for example MS Office 97-2003</li>
<li><strong>OpX</strong>: the file type is OpenXML, for example MS Office 2007+</li>
<li><strong>XML</strong>: the file type is Word 2003 XML</li>
<li><strong>MHT</strong>: the file type is Word MHTML, aka Single File Web Page (.mht)</li>
<li><strong>?</strong>: the file type is not supported</li>
<li><strong>M</strong>: contains VBA Macros</li>
<li><strong>A</strong>: auto-executable macros</li>
<li><strong>S</strong>: suspicious VBA keywords</li>
<li><strong>I</strong>: potential IOCs</li>
<li><strong>H</strong>: hex-encoded strings (potential obfuscation)</li>
<li><strong>B</strong>: Base64-encoded strings (potential obfuscation)</li>
<li><strong>D</strong>: Dridex-encoded strings (potential obfuscation)</li>
<li><strong>V</strong>: VBA string expressions (potential obfuscation)</li>
</ul>
<p>Here is an example:</p>
<pre class="text"><code>c:\&gt;olevba \MalwareZoo\VBA\samples\*
Flags       Filename
----------- -----------------------------------------------------------------
OLE:MASI--- \MalwareZoo\VBA\samples\DIAN_caso-5415.doc.malware
OLE:MASIH-- \MalwareZoo\VBA\samples\DRIDEX_1.doc.malware
OLE:MASIH-- \MalwareZoo\VBA\samples\DRIDEX_2.doc.malware
OLE:MASI--- \MalwareZoo\VBA\samples\DRIDEX_3.doc.malware
OLE:MASIH-- \MalwareZoo\VBA\samples\DRIDEX_4.doc.malware
OLE:MASIH-- \MalwareZoo\VBA\samples\DRIDEX_5.doc.malware
OLE:MASIH-- \MalwareZoo\VBA\samples\DRIDEX_6.doc.malware
OLE:MAS---- \MalwareZoo\VBA\samples\DRIDEX_7.doc.malware
OLE:MASIH-- \MalwareZoo\VBA\samples\DRIDEX_8.doc.malware
OLE:MASIHBD \MalwareZoo\VBA\samples\DRIDEX_9.xls.malware
OLE:MASIH-- \MalwareZoo\VBA\samples\DRIDEX_A.doc.malware
OLE:------- \MalwareZoo\VBA\samples\Normal_Document.doc
OLE:M------ \MalwareZoo\VBA\samples\Normal_Document_Macro.doc
OpX:MASI--- \MalwareZoo\VBA\samples\RottenKitten.xlsb.malware
OLE:MASI-B- \MalwareZoo\VBA\samples\ROVNIX.doc.malware
OLE:MA----- \MalwareZoo\VBA\samples\Word within Word macro auto.doc</code></pre>
<h2 id="python-3-support---olevba3">Python 3 support - olevba3</h2>
<p>Since v0.54, olevba is fully compatible with both Python 2 and 3. There is no need to use olevba3 anymore, however it is still present for backward compatibility.</p>
<hr />
<h2 id="how-to-use-olevba-in-python-applications">How to use olevba in Python applications</h2>
<p>olevba may be used to open a MS Office file, detect if it contains VBA macros, extract and analyze the VBA source code from your own python applications.</p>
<p>IMPORTANT: olevba is currently under active development, therefore this API is likely to change.</p>
<h3 id="import-olevba">Import olevba</h3>
<p>First, import the <strong>oletools.olevba</strong> package, using at least the VBA_Parser and VBA_Scanner classes:</p>
<div class="sourceCode" id="cb12"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb12-1" data-line-number="1"><span class="im">from</span> oletools.olevba <span class="im">import</span> VBA_Parser, TYPE_OLE, TYPE_OpenXML, TYPE_Word2003_XML, TYPE_MHTML</a></code></pre></div>
<h3 id="parse-a-ms-office-file---vba_parser">Parse a MS Office file - VBA_Parser</h3>
<p>To parse a file on disk, create an instance of the <strong>VBA_Parser</strong> class, providing the name of the file to open as parameter. For example:</p>
<div class="sourceCode" id="cb13"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb13-1" data-line-number="1">vbaparser <span class="op">=</span> VBA_Parser(<span class="st">&#39;my_file_with_macros.doc&#39;</span>)</a></code></pre></div>
<p>The file may also be provided as a bytes string containing its data. In that case, the actual filename must be provided for reference, and the file content with the data parameter. For example:</p>
<div class="sourceCode" id="cb14"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb14-1" data-line-number="1">myfile <span class="op">=</span> <span class="st">&#39;my_file_with_macros.doc&#39;</span></a>
<a class="sourceLine" id="cb14-2" data-line-number="2">filedata <span class="op">=</span> <span class="bu">open</span>(myfile, <span class="st">&#39;rb&#39;</span>).read()</a>
<a class="sourceLine" id="cb14-3" data-line-number="3">vbaparser <span class="op">=</span> VBA_Parser(myfile, data<span class="op">=</span>filedata)</a></code></pre></div>
<p>VBA_Parser will raise an exception if the file is not a supported format, such as OLE (MS Office 97-2003), OpenXML (MS Office 2007+), MHTML or Word 2003 XML.</p>
<p>After parsing the file, the attribute <strong>VBA_Parser.type</strong> is a string indicating the file type. It can be either TYPE_OLE, TYPE_OpenXML, TYPE_Word2003_XML or TYPE_MHTML. (constants defined in the olevba module)</p>
<h3 id="detect-vba-macros">Detect VBA macros</h3>
<p>The method <strong>detect_vba_macros</strong> of a VBA_Parser object returns True if VBA macros have been found in the file, False otherwise.</p>
<div class="sourceCode" id="cb15"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb15-1" data-line-number="1"><span class="cf">if</span> vbaparser.detect_vba_macros():</a>
<a class="sourceLine" id="cb15-2" data-line-number="2">    <span class="bu">print</span> <span class="st">&#39;VBA Macros found&#39;</span></a>
<a class="sourceLine" id="cb15-3" data-line-number="3"><span class="cf">else</span>:</a>
<a class="sourceLine" id="cb15-4" data-line-number="4">    <span class="bu">print</span> <span class="st">&#39;No VBA Macros found&#39;</span></a></code></pre></div>
<p>Note: The detection algorithm looks for streams and storage with specific names in the OLE structure, which works fine for all the supported formats listed above. However, for some formats such as PowerPoint 97-2003, this method will always return False because VBA Macros are stored in a different way which is not yet supported by olevba.</p>
<p>Moreover, if the file contains an embedded document (e.g. an Excel workbook inserted into a Word document), this method may return True if the embedded document contains VBA Macros, even if the main document does not.</p>
<h3 id="extract-vba-macro-source-code">Extract VBA Macro Source Code</h3>
<p>The method <strong>extract_macros</strong> extracts and decompresses source code for each VBA macro found in the file (possibly including embedded files). It is a generator yielding a tuple (filename, stream_path, vba_filename, vba_code) for each VBA macro found.</p>
<ul>
<li>filename: If the file is OLE (MS Office 97-2003), filename is the path of the file. If the file is OpenXML (MS Office 2007+), filename is the path of the OLE subfile containing VBA macros within the zip archive, e.g. word/vbaProject.bin.</li>
<li>stream_path: path of the OLE stream containing the VBA macro source code</li>
<li>vba_filename: corresponding VBA filename</li>
<li>vba_code: string containing the VBA source code in clear text</li>
</ul>
<p>Example:</p>
<div class="sourceCode" id="cb16"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb16-1" data-line-number="1"><span class="cf">for</span> (filename, stream_path, vba_filename, vba_code) <span class="kw">in</span> vbaparser.extract_macros():</a>
<a class="sourceLine" id="cb16-2" data-line-number="2">    <span class="bu">print</span> <span class="st">&#39;-&#39;</span><span class="op">*</span><span class="dv">79</span></a>
<a class="sourceLine" id="cb16-3" data-line-number="3">    <span class="bu">print</span> <span class="st">&#39;Filename    :&#39;</span>, filename</a>
<a class="sourceLine" id="cb16-4" data-line-number="4">    <span class="bu">print</span> <span class="st">&#39;OLE stream  :&#39;</span>, stream_path</a>
<a class="sourceLine" id="cb16-5" data-line-number="5">    <span class="bu">print</span> <span class="st">&#39;VBA filename:&#39;</span>, vba_filename</a>
<a class="sourceLine" id="cb16-6" data-line-number="6">    <span class="bu">print</span> <span class="st">&#39;- &#39;</span><span class="op">*</span><span class="dv">39</span></a>
<a class="sourceLine" id="cb16-7" data-line-number="7">    <span class="bu">print</span> vba_code</a></code></pre></div>
<p>Alternatively, the VBA_Parser method <strong>extract_all_macros</strong> returns the same results as a list of tuples.</p>
<h3 id="analyze-vba-source-code">Analyze VBA Source Code</h3>
<p>Since version 0.40, the VBA_Parser class provides simpler methods than VBA_Scanner to analyze all macros contained in a file:</p>
<p>The method <strong>analyze_macros</strong> from the class <strong>VBA_Parser</strong> can be used to scan the source code of all VBA modules to find obfuscated strings, suspicious keywords, IOCs, auto-executable macros, etc.</p>
<p>analyze_macros() takes an optional argument show_decoded_strings: if set to True, the results will contain all the encoded strings found in the code (Hex, Base64, Dridex) with their decoded value. By default, it will only include the strings which contain printable characters.</p>
<p><strong>VBA_Parser.analyze_macros()</strong> returns a list of tuples (type, keyword, description), one for each item in the results.</p>
<ul>
<li>type may be either ‘AutoExec’, ‘Suspicious’, ‘IOC’, ‘Hex String’, ‘Base64 String’, ‘Dridex String’ or ‘VBA obfuscated Strings’.</li>
<li>keyword is the string found for auto-executable macros, suspicious keywords or IOCs. For obfuscated strings, it is the decoded value of the string.</li>
<li>description provides a description of the keyword. For obfuscated strings, it is the encoded value of the string.</li>
</ul>
<p>Example:</p>
<div class="sourceCode" id="cb17"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb17-1" data-line-number="1">results <span class="op">=</span> vbaparser.analyze_macros()</a>
<a class="sourceLine" id="cb17-2" data-line-number="2"><span class="cf">for</span> kw_type, keyword, description <span class="kw">in</span> results:</a>
<a class="sourceLine" id="cb17-3" data-line-number="3">    <span class="bu">print</span> <span class="st">&#39;type=</span><span class="sc">%s</span><span class="st"> - keyword=</span><span class="sc">%s</span><span class="st"> - description=</span><span class="sc">%s</span><span class="st">&#39;</span> <span class="op">%</span> (kw_type, keyword, description)</a></code></pre></div>
<p>After calling analyze_macros, the following VBA_Parser attributes also provide the number of items found for each category:</p>
<div class="sourceCode" id="cb18"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb18-1" data-line-number="1"><span class="bu">print</span> <span class="st">&#39;AutoExec keywords: </span><span class="sc">%d</span><span class="st">&#39;</span> <span class="op">%</span> vbaparser.nb_autoexec</a>
<a class="sourceLine" id="cb18-2" data-line-number="2"><span class="bu">print</span> <span class="st">&#39;Suspicious keywords: </span><span class="sc">%d</span><span class="st">&#39;</span> <span class="op">%</span> vbaparser.nb_suspicious</a>
<a class="sourceLine" id="cb18-3" data-line-number="3"><span class="bu">print</span> <span class="st">&#39;IOCs: </span><span class="sc">%d</span><span class="st">&#39;</span> <span class="op">%</span> vbaparser.nb_iocs</a>
<a class="sourceLine" id="cb18-4" data-line-number="4"><span class="bu">print</span> <span class="st">&#39;Hex obfuscated strings: </span><span class="sc">%d</span><span class="st">&#39;</span> <span class="op">%</span> vbaparser.nb_hexstrings</a>
<a class="sourceLine" id="cb18-5" data-line-number="5"><span class="bu">print</span> <span class="st">&#39;Base64 obfuscated strings: </span><span class="sc">%d</span><span class="st">&#39;</span> <span class="op">%</span> vbaparser.nb_base64strings</a>
<a class="sourceLine" id="cb18-6" data-line-number="6"><span class="bu">print</span> <span class="st">&#39;Dridex obfuscated strings: </span><span class="sc">%d</span><span class="st">&#39;</span> <span class="op">%</span> vbaparser.nb_dridexstrings</a>
<a class="sourceLine" id="cb18-7" data-line-number="7"><span class="bu">print</span> <span class="st">&#39;VBA obfuscated strings: </span><span class="sc">%d</span><span class="st">&#39;</span> <span class="op">%</span> vbaparser.nb_vbastrings</a></code></pre></div>
<h3 id="deobfuscate-vba-macro-source-code">Deobfuscate VBA Macro Source Code</h3>
<p>The method <strong>reveal</strong> attempts to deobfuscate the macro source code by replacing all the obfuscated strings by their decoded content. Returns a single string.</p>
<p>Example:</p>
<div class="sourceCode" id="cb19"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb19-1" data-line-number="1"><span class="bu">print</span> vbaparser.reveal()</a></code></pre></div>
<h3 id="close-the-vba_parser">Close the VBA_Parser</h3>
<p>After usage, it is better to call the <strong>close</strong> method of the VBA_Parser object, to make sure the file is closed, especially if your application is parsing many files.</p>
<div class="sourceCode" id="cb20"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb20-1" data-line-number="1">vbaparser.close()</a></code></pre></div>
<hr />
<h2 id="deprecated-api">Deprecated API</h2>
<p>The following methods and functions are still functional, but their usage is not recommended since they have been replaced by better solutions.</p>
<h3 id="vba_scanner-deprecated">VBA_Scanner (deprecated)</h3>
<p>The class <strong>VBA_Scanner</strong> can be used to scan the source code of a VBA module to find obfuscated strings, suspicious keywords, IOCs, auto-executable macros, etc.</p>
<p>First, create a VBA_Scanner object with a string containing the VBA source code (for example returned by the extract_macros method). Then call the methods <strong>scan</strong> or <strong>scan_summary</strong> to get the results of the analysis.</p>
<p>scan() takes an optional argument include_decoded_strings: if set to True, the results will contain all the encoded strings found in the code (Hex, Base64, Dridex) with their decoded value.</p>
<p><strong>scan</strong> returns a list of tuples (type, keyword, description), one for each item in the results.</p>
<ul>
<li>type may be either ‘AutoExec’, ‘Suspicious’, ‘IOC’, ‘Hex String’, ‘Base64 String’ or ‘Dridex String’.</li>
<li>keyword is the string found for auto-executable macros, suspicious keywords or IOCs. For obfuscated strings, it is the decoded value of the string.</li>
<li>description provides a description of the keyword. For obfuscated strings, it is the encoded value of the string.</li>
</ul>
<p>Example:</p>
<div class="sourceCode" id="cb21"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb21-1" data-line-number="1">vba_scanner <span class="op">=</span> VBA_Scanner(vba_code)</a>
<a class="sourceLine" id="cb21-2" data-line-number="2">results <span class="op">=</span> vba_scanner.scan(include_decoded_strings<span class="op">=</span><span class="va">True</span>)</a>
<a class="sourceLine" id="cb21-3" data-line-number="3"><span class="cf">for</span> kw_type, keyword, description <span class="kw">in</span> results:</a>
<a class="sourceLine" id="cb21-4" data-line-number="4">    <span class="bu">print</span> <span class="st">&#39;type=</span><span class="sc">%s</span><span class="st"> - keyword=</span><span class="sc">%s</span><span class="st"> - description=</span><span class="sc">%s</span><span class="st">&#39;</span> <span class="op">%</span> (kw_type, keyword, description)</a></code></pre></div>
<p>The function <strong>scan_vba</strong> is a shortcut for VBA_Scanner(vba_code).scan():</p>
<div class="sourceCode" id="cb22"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb22-1" data-line-number="1">results <span class="op">=</span> scan_vba(vba_code, include_decoded_strings<span class="op">=</span><span class="va">True</span>)</a>
<a class="sourceLine" id="cb22-2" data-line-number="2"><span class="cf">for</span> kw_type, keyword, description <span class="kw">in</span> results:</a>
<a class="sourceLine" id="cb22-3" data-line-number="3">    <span class="bu">print</span> <span class="st">&#39;type=</span><span class="sc">%s</span><span class="st"> - keyword=</span><span class="sc">%s</span><span class="st"> - description=</span><span class="sc">%s</span><span class="st">&#39;</span> <span class="op">%</span> (kw_type, keyword, description)</a></code></pre></div>
<p><strong>scan_summary</strong> returns a tuple with the number of items found for each category: (autoexec, suspicious, IOCs, hex, base64, dridex).</p>
<h3 id="detect-auto-executable-macros-deprecated">Detect auto-executable macros (deprecated)</h3>
<p><strong>Deprecated</strong>: It is preferable to use either scan_vba or VBA_Scanner to get all results at once.</p>
<p>The function <strong>detect_autoexec</strong> checks if VBA macro code contains specific macro names that will be triggered when the document/workbook is opened, closed, changed, etc.</p>
<p>It returns a list of tuples containing two strings, the detected keyword, and the description of the trigger. (See the malware example above)</p>
<p>Sample usage:</p>
<div class="sourceCode" id="cb23"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb23-1" data-line-number="1"><span class="im">from</span> oletools.olevba <span class="im">import</span> detect_autoexec</a>
<a class="sourceLine" id="cb23-2" data-line-number="2">autoexec_keywords <span class="op">=</span> detect_autoexec(vba_code)</a>
<a class="sourceLine" id="cb23-3" data-line-number="3"><span class="cf">if</span> autoexec_keywords:</a>
<a class="sourceLine" id="cb23-4" data-line-number="4">    <span class="bu">print</span> <span class="st">&#39;Auto-executable macro keywords found:&#39;</span></a>
<a class="sourceLine" id="cb23-5" data-line-number="5">    <span class="cf">for</span> keyword, description <span class="kw">in</span> autoexec_keywords:</a>
<a class="sourceLine" id="cb23-6" data-line-number="6">        <span class="bu">print</span> <span class="st">&#39;</span><span class="sc">%s</span><span class="st">: </span><span class="sc">%s</span><span class="st">&#39;</span> <span class="op">%</span> (keyword, description)</a>
<a class="sourceLine" id="cb23-7" data-line-number="7"><span class="cf">else</span>:</a>
<a class="sourceLine" id="cb23-8" data-line-number="8">    <span class="bu">print</span> <span class="st">&#39;Auto-executable macro keywords: None found&#39;</span></a></code></pre></div>
<h3 id="detect-suspicious-vba-keywords-deprecated">Detect suspicious VBA keywords (deprecated)</h3>
<p><strong>Deprecated</strong>: It is preferable to use either scan_vba or VBA_Scanner to get all results at once.</p>
<p>The function <strong>detect_suspicious</strong> checks if VBA macro code contains specific keywords often used by malware to act on the system (create files, run commands or applications, write to the registry, etc).</p>
<p>It returns a list of tuples containing two strings, the detected keyword, and the description of the corresponding malicious behaviour. (See the malware example above)</p>
<p>Sample usage:</p>
<div class="sourceCode" id="cb24"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb24-1" data-line-number="1"><span class="im">from</span> oletools.olevba <span class="im">import</span> detect_suspicious</a>
<a class="sourceLine" id="cb24-2" data-line-number="2">suspicious_keywords <span class="op">=</span> detect_suspicious(vba_code)</a>
<a class="sourceLine" id="cb24-3" data-line-number="3"><span class="cf">if</span> suspicious_keywords:</a>
<a class="sourceLine" id="cb24-4" data-line-number="4">    <span class="bu">print</span> <span class="st">&#39;Suspicious VBA keywords found:&#39;</span></a>
<a class="sourceLine" id="cb24-5" data-line-number="5">    <span class="cf">for</span> keyword, description <span class="kw">in</span> suspicious_keywords:</a>
<a class="sourceLine" id="cb24-6" data-line-number="6">        <span class="bu">print</span> <span class="st">&#39;</span><span class="sc">%s</span><span class="st">: </span><span class="sc">%s</span><span class="st">&#39;</span> <span class="op">%</span> (keyword, description)</a>
<a class="sourceLine" id="cb24-7" data-line-number="7"><span class="cf">else</span>:</a>
<a class="sourceLine" id="cb24-8" data-line-number="8">    <span class="bu">print</span> <span class="st">&#39;Suspicious VBA keywords: None found&#39;</span></a></code></pre></div>
<h3 id="extract-potential-iocs-deprecated">Extract potential IOCs (deprecated)</h3>
<p><strong>Deprecated</strong>: It is preferable to use either scan_vba or VBA_Scanner to get all results at once.</p>
<p>The function <strong>detect_patterns</strong> checks if VBA macro code contains specific patterns of interest, that may be useful for malware analysis and detection (potential Indicators of Compromise): IP addresses, e-mail addresses, URLs, executable file names.</p>
<p>It returns a list of tuples containing two strings, the pattern type, and the extracted value. (See the malware example above)</p>
<p>Sample usage:</p>
<div class="sourceCode" id="cb25"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb25-1" data-line-number="1"><span class="im">from</span> oletools.olevba <span class="im">import</span> detect_patterns</a>
<a class="sourceLine" id="cb25-2" data-line-number="2">patterns <span class="op">=</span> detect_patterns(vba_code)</a>
<a class="sourceLine" id="cb25-3" data-line-number="3"><span class="cf">if</span> patterns:</a>
<a class="sourceLine" id="cb25-4" data-line-number="4">    <span class="bu">print</span> <span class="st">&#39;Patterns found:&#39;</span></a>
<a class="sourceLine" id="cb25-5" data-line-number="5">    <span class="cf">for</span> pattern_type, value <span class="kw">in</span> patterns:</a>
<a class="sourceLine" id="cb25-6" data-line-number="6">        <span class="bu">print</span> <span class="st">&#39;</span><span class="sc">%s</span><span class="st">: </span><span class="sc">%s</span><span class="st">&#39;</span> <span class="op">%</span> (pattern_type, value)</a>
<a class="sourceLine" id="cb25-7" data-line-number="7"><span class="cf">else</span>:</a>
<a class="sourceLine" id="cb25-8" data-line-number="8">    <span class="bu">print</span> <span class="st">&#39;Patterns: None found&#39;</span></a></code></pre></div>
<hr />
<h2 id="python-oletools-documentation">python-oletools documentation</h2>
<ul>
<li><a href="Home.html">Home</a></li>
<li><a href="License.html">License</a></li>
<li><a href="Install.html">Install</a></li>
<li><a href="Contribute.html">Contribute</a>, Suggest Improvements or Report Issues</li>
<li>Tools:
<ul>
<li><a href="mraptor.html">mraptor</a></li>
<li><a href="msodde.html">msodde</a></li>
<li><a href="olebrowse.html">olebrowse</a></li>
<li><a href="oledir.html">oledir</a></li>
<li><a href="oleid.html">oleid</a></li>
<li><a href="olemap.html">olemap</a></li>
<li><a href="olemeta.html">olemeta</a></li>
<li><a href="oleobj.html">oleobj</a></li>
<li><a href="oletimes.html">oletimes</a></li>
<li><a href="olevba.html">olevba</a></li>
<li><a href="pyxswf.html">pyxswf</a></li>
<li><a href="rtfobj.html">rtfobj</a></li>
</ul></li>
</ul>
</body>
</html>
