<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>Untitled</title>
  <style type="text/css">
      code{white-space: pre-wrap;}
      span.smallcaps{font-variant: small-caps;}
      span.underline{text-decoration: underline;}
      div.column{display: inline-block; vertical-align: top; width: 50%;}
  </style>
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<h1 id="oletimes">oletimes</h1>
<p>oletimes is a script to parse OLE files such as MS Office documents (e.g. Word, Excel), to extract creation and modification times of all streams and storages in the OLE file.</p>
<p>It is part of the <a href="http://www.decalage.info/python/oletools">python-oletools</a> package.</p>
<h2 id="usage">Usage</h2>
<pre class="text"><code>oletimes &lt;file&gt;</code></pre>
<h3 id="example">Example</h3>
<p>Checking the malware sample <a href="https://malwr.com/analysis/M2I4YWRhM2IwY2QwNDljN2E3ZWFjYTg3ODk4NmZhYmE/">DIAN_caso-5415.doc</a>:</p>
<pre class="text"><code>&gt;oletimes DIAN_caso-5415.doc

+----------------------------+---------------------+---------------------+
| Stream/Storage name        | Modification Time   | Creation Time       |
+----------------------------+---------------------+---------------------+
| Root                       | 2014-05-14 12:45:24 | None                |
| &#39;\x01CompObj&#39;              | None                | None                |
| &#39;\x05DocumentSummaryInform | None                | None                |
| ation&#39;                     |                     |                     |
| &#39;\x05SummaryInformation&#39;   | None                | None                |
| &#39;1Table&#39;                   | None                | None                |
| &#39;Data&#39;                     | None                | None                |
| &#39;Macros&#39;                   | 2014-05-14 12:45:24 | 2014-05-14 12:45:24 |
| &#39;Macros/PROJECT&#39;           | None                | None                |
| &#39;Macros/PROJECTwm&#39;         | None                | None                |
| &#39;Macros/VBA&#39;               | 2014-05-14 12:45:24 | 2014-05-14 12:45:24 |
| &#39;Macros/VBA/ThisDocument&#39;  | None                | None                |
| &#39;Macros/VBA/_VBA_PROJECT&#39;  | None                | None                |
| &#39;Macros/VBA/__SRP_0&#39;       | None                | None                |
| &#39;Macros/VBA/__SRP_1&#39;       | None                | None                |
| &#39;Macros/VBA/__SRP_2&#39;       | None                | None                |
| &#39;Macros/VBA/__SRP_3&#39;       | None                | None                |
| &#39;Macros/VBA/dir&#39;           | None                | None                |
| &#39;WordDocument&#39;             | None                | None                |
+----------------------------+---------------------+---------------------+</code></pre>
<h2 id="how-to-use-oletimes-in-python-applications">How to use oletimes in Python applications</h2>
<p>TODO</p>
<hr />
<h2 id="python-oletools-documentation">python-oletools documentation</h2>
<ul>
<li><a href="Home.html">Home</a></li>
<li><a href="License.html">License</a></li>
<li><a href="Install.html">Install</a></li>
<li><a href="Contribute.html">Contribute</a>, Suggest Improvements or Report Issues</li>
<li>Tools:
<ul>
<li><a href="mraptor.html">mraptor</a></li>
<li><a href="msodde.html">msodde</a></li>
<li><a href="olebrowse.html">olebrowse</a></li>
<li><a href="oledir.html">oledir</a></li>
<li><a href="oleid.html">oleid</a></li>
<li><a href="olemap.html">olemap</a></li>
<li><a href="olemeta.html">olemeta</a></li>
<li><a href="oleobj.html">oleobj</a></li>
<li><a href="oletimes.html">oletimes</a></li>
<li><a href="olevba.html">olevba</a></li>
<li><a href="pyxswf.html">pyxswf</a></li>
<li><a href="rtfobj.html">rtfobj</a></li>
</ul></li>
</ul>
</body>
</html>
