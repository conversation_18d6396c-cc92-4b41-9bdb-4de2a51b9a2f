How to Suggest Improvements, Report Issues or Contribute
========================================================

This is a personal open-source project, developed on my spare time.
Any contribution, suggestion, feedback or bug report is welcome.

To **suggest improvements, report a bug or any issue**,
please use the [issue reporting page](https://github.com/decalage2/oletools/issues),
and provide all the information and files to reproduce the problem.

You may also [contact the author](http://decalage.info/contact) directly
to **send feedback**.

The code is available in [a repository on GitHub](https://github.com/decalage2/oletools).
You may use it to **submit enhancements** using forks and pull requests.

--------------------------------------------------------------------------

python-oletools documentation
-----------------------------

- [[Home]]
- [[License]]
- [[Install]]
- [[Contribute]], Suggest Improvements or Report Issues
- Tools:
	- [[mraptor]]
	- [[msodde]]
	- [[olebrowse]]
	- [[oledir]]
	- [[oleid]]
	- [[olemap]]
	- [[olemeta]]
	- [[oleobj]]
	- [[oletimes]]
	- [[olevba]]
	- [[pyxswf]]
	- [[rtfobj]]
