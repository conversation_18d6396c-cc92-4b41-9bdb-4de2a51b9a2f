<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>Untitled</title>
  <style type="text/css">
      code{white-space: pre-wrap;}
      span.smallcaps{font-variant: small-caps;}
      span.underline{text-decoration: underline;}
      div.column{display: inline-block; vertical-align: top; width: 50%;}
  </style>
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<h1 id="olebrowse">olebrowse</h1>
<p>olebrowse is a simple GUI to browse OLE files (e.g. MS Word, Excel, Powerpoint documents), to view and extract individual data streams.</p>
<p>It is part of the <a href="http://www.decalage.info/python/oletools">python-oletools</a> package.</p>
<h2 id="dependencies">Dependencies</h2>
<p>olebrowse requires <a href="https://en.wikipedia.org/wiki/Tkinter">Tkinter</a>. On Windows and MacOSX, it should be installed with Python, and olebrowse should work out of the box.</p>
<p>However, on Linux it might be necessary to install the tkinter package for Python separately. For example, on Ubuntu this is done with the following command:</p>
<pre><code>sudo apt-get install python-tk</code></pre>
<p>And for Python 3:</p>
<pre><code>sudo apt-get install python3-tk</code></pre>
<h2 id="usage">Usage</h2>
<pre><code>olebrowse [file]</code></pre>
<p>If you provide a file it will be opened, else a dialog will allow you to browse folders to open a file. Then if it is a valid OLE file, the list of data streams will be displayed. You can select a stream, and then either view its content in a builtin hexadecimal viewer, or save it to a file for further analysis.</p>
<h2 id="screenshots">Screenshots</h2>
<p>Main menu, showing all streams in the OLE file:</p>
<p><img src="olebrowse1_menu.png" /></p>
<p>Menu with actions for a stream:</p>
<p><img src="olebrowse2_stream.png" /></p>
<p>Hex view for a stream:</p>
<p><img src="olebrowse3_hexview.png" /></p>
<hr />
<h2 id="python-oletools-documentation">python-oletools documentation</h2>
<ul>
<li><a href="Home.html">Home</a></li>
<li><a href="License.html">License</a></li>
<li><a href="Install.html">Install</a></li>
<li><a href="Contribute.html">Contribute</a>, Suggest Improvements or Report Issues</li>
<li>Tools:
<ul>
<li><a href="mraptor.html">mraptor</a></li>
<li><a href="msodde.html">msodde</a></li>
<li><a href="olebrowse.html">olebrowse</a></li>
<li><a href="oledir.html">oledir</a></li>
<li><a href="oleid.html">oleid</a></li>
<li><a href="olemap.html">olemap</a></li>
<li><a href="olemeta.html">olemeta</a></li>
<li><a href="oleobj.html">oleobj</a></li>
<li><a href="oletimes.html">oletimes</a></li>
<li><a href="olevba.html">olevba</a></li>
<li><a href="pyxswf.html">pyxswf</a></li>
<li><a href="rtfobj.html">rtfobj</a></li>
</ul></li>
</ul>
</body>
</html>
