<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>Untitled</title>
  <style type="text/css">
      code{white-space: pre-wrap;}
      span.smallcaps{font-variant: small-caps;}
      span.underline{text-decoration: underline;}
      div.column{display: inline-block; vertical-align: top; width: 50%;}
  </style>
  <style type="text/css">
a.sourceLine { display: inline-block; line-height: 1.25; }
a.sourceLine { pointer-events: none; color: inherit; text-decoration: inherit; }
a.sourceLine:empty { height: 1.2em; position: absolute; }
.sourceCode { overflow: visible; }
code.sourceCode { white-space: pre; position: relative; }
div.sourceCode { margin: 1em 0; }
pre.sourceCode { margin: 0; }
@media screen {
div.sourceCode { overflow: auto; }
}
@media print {
code.sourceCode { white-space: pre-wrap; }
a.sourceLine { text-indent: -1em; padding-left: 1em; }
}
pre.numberSource a.sourceLine
  { position: relative; }
pre.numberSource a.sourceLine:empty
  { position: absolute; }
pre.numberSource a.sourceLine::before
  { content: attr(data-line-number);
    position: absolute; left: -5em; text-align: right; vertical-align: baseline;
    border: none; pointer-events: all;
    -webkit-touch-callout: none; -webkit-user-select: none;
    -khtml-user-select: none; -moz-user-select: none;
    -ms-user-select: none; user-select: none;
    padding: 0 4px; width: 4em;
    color: #aaaaaa;
  }
pre.numberSource { margin-left: 3em; border-left: 1px solid #aaaaaa;  padding-left: 4px; }
div.sourceCode
  {  }
@media screen {
a.sourceLine::before { text-decoration: underline; }
}
code span.al { color: #ff0000; font-weight: bold; } /* Alert */
code span.an { color: #60a0b0; font-weight: bold; font-style: italic; } /* Annotation */
code span.at { color: #7d9029; } /* Attribute */
code span.bn { color: #40a070; } /* BaseN */
code span.bu { } /* BuiltIn */
code span.cf { color: #007020; font-weight: bold; } /* ControlFlow */
code span.ch { color: #4070a0; } /* Char */
code span.cn { color: #880000; } /* Constant */
code span.co { color: #60a0b0; font-style: italic; } /* Comment */
code span.cv { color: #60a0b0; font-weight: bold; font-style: italic; } /* CommentVar */
code span.do { color: #ba2121; font-style: italic; } /* Documentation */
code span.dt { color: #902000; } /* DataType */
code span.dv { color: #40a070; } /* DecVal */
code span.er { color: #ff0000; font-weight: bold; } /* Error */
code span.ex { } /* Extension */
code span.fl { color: #40a070; } /* Float */
code span.fu { color: #06287e; } /* Function */
code span.im { } /* Import */
code span.in { color: #60a0b0; font-weight: bold; font-style: italic; } /* Information */
code span.kw { color: #007020; font-weight: bold; } /* Keyword */
code span.op { color: #666666; } /* Operator */
code span.ot { color: #007020; } /* Other */
code span.pp { color: #bc7a00; } /* Preprocessor */
code span.sc { color: #4070a0; } /* SpecialChar */
code span.ss { color: #bb6688; } /* SpecialString */
code span.st { color: #4070a0; } /* String */
code span.va { color: #19177c; } /* Variable */
code span.vs { color: #4070a0; } /* VerbatimString */
code span.wa { color: #60a0b0; font-weight: bold; font-style: italic; } /* Warning */
  </style>
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<h1 id="oleid">oleid</h1>
<p>oleid is a script to analyze OLE files such as MS Office documents (e.g. Word, Excel), to detect specific characteristics usually found in malicious files (e.g. malware). For example it can detect VBA macros and embedded Flash objects.</p>
<p>It is part of the <a href="http://www.decalage.info/python/oletools">python-oletools</a> package.</p>
<h2 id="main-features">Main Features</h2>
<ul>
<li>Detect OLE file type from its internal structure (e.g. MS Word, Excel, PowerPoint, …)</li>
<li>Detect VBA Macros</li>
<li>Detect embedded Flash objects</li>
<li>Detect embedded OLE objects</li>
<li>Detect MS Office encryption</li>
<li>Can be used as a command-line tool</li>
<li>Python API to integrate it in your applications</li>
</ul>
<p>Planned improvements:</p>
<ul>
<li>Extract the most important metadata fields</li>
<li>Support for OpenXML files and embedded OLE files</li>
<li>Generic VBA macros detection</li>
<li>Detect auto-executable VBA macros</li>
<li>Extended OLE file types detection</li>
<li>Detect unusual OLE structures (fragmentation, unused sectors, etc)</li>
<li>Options to scan multiple files</li>
<li>Options to scan files from encrypted zip archives</li>
<li>CSV output</li>
</ul>
<h2 id="usage">Usage</h2>
<pre class="text"><code>oleid &lt;file&gt;</code></pre>
<h3 id="example">Example</h3>
<p>Analyzing a Word document containing a Flash object and VBA macros:</p>
<pre class="text"><code>C:\oletools&gt;oleid word_flash_vba.doc

Filename: word_flash_vba.doc
+-------------------------------+-----------------------+
| Indicator                     | Value                 |
+-------------------------------+-----------------------+
| OLE format                    | True                  |
| Has SummaryInformation stream | True                  |
| Application name              | Microsoft Office Word |
| Encrypted                     | False                 |
| Word Document                 | True                  |
| VBA Macros                    | True                  |
| Excel Workbook                | False                 |
| PowerPoint Presentation       | False                 |
| Visio Drawing                 | False                 |
| ObjectPool                    | True                  |
| Flash objects                 | 1                     |
+-------------------------------+-----------------------+</code></pre>
<h2 id="how-to-use-oleid-in-your-python-applications">How to use oleid in your Python applications</h2>
<p>First, import oletools.oleid, and create an <strong>OleID</strong> object to scan a file:</p>
<div class="sourceCode" id="cb3"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb3-1" data-line-number="1"><span class="im">import</span> oletools.oleid</a>
<a class="sourceLine" id="cb3-2" data-line-number="2"></a>
<a class="sourceLine" id="cb3-3" data-line-number="3">oid <span class="op">=</span> oletools.oleid.OleID(filename)</a></code></pre></div>
<p>Note: filename can be a filename, a file-like object, or a bytes string containing the file to be analyzed.</p>
<p>Second, call the <strong>check()</strong> method. It returns a list of <strong>Indicator</strong> objects.</p>
<p>Each Indicator object has the following attributes:</p>
<ul>
<li><strong>id</strong>: str, identifier for the indicator</li>
<li><strong>name</strong>: str, name to display the indicator</li>
<li><strong>description</strong>: str, long description of the indicator</li>
<li><strong>type</strong>: class of the indicator (e.g. bool, str, int)</li>
<li><strong>value</strong>: value of the indicator</li>
</ul>
<p>For example, the following code displays all the indicators:</p>
<div class="sourceCode" id="cb4"><pre class="sourceCode python"><code class="sourceCode python"><a class="sourceLine" id="cb4-1" data-line-number="1">indicators <span class="op">=</span> oid.check()</a>
<a class="sourceLine" id="cb4-2" data-line-number="2"><span class="cf">for</span> i <span class="kw">in</span> indicators:</a>
<a class="sourceLine" id="cb4-3" data-line-number="3">    <span class="bu">print</span> <span class="st">&#39;Indicator id=</span><span class="sc">%s</span><span class="st"> name=&quot;</span><span class="sc">%s</span><span class="st">&quot; type=</span><span class="sc">%s</span><span class="st"> value=</span><span class="sc">%s</span><span class="st">&#39;</span> <span class="op">%</span> (i.<span class="bu">id</span>, i.name, i.<span class="bu">type</span>, <span class="bu">repr</span>(i.value))</a>
<a class="sourceLine" id="cb4-4" data-line-number="4">    <span class="bu">print</span> <span class="st">&#39;description:&#39;</span>, i.description</a>
<a class="sourceLine" id="cb4-5" data-line-number="5">    <span class="bu">print</span> <span class="st">&#39;&#39;</span></a></code></pre></div>
<p>See the source code of oleid.py for more details.</p>
<hr />
<h2 id="python-oletools-documentation">python-oletools documentation</h2>
<ul>
<li><a href="Home.html">Home</a></li>
<li><a href="License.html">License</a></li>
<li><a href="Install.html">Install</a></li>
<li><a href="Contribute.html">Contribute</a>, Suggest Improvements or Report Issues</li>
<li>Tools:
<ul>
<li><a href="mraptor.html">mraptor</a></li>
<li><a href="msodde.html">msodde</a></li>
<li><a href="olebrowse.html">olebrowse</a></li>
<li><a href="oledir.html">oledir</a></li>
<li><a href="oleid.html">oleid</a></li>
<li><a href="olemap.html">olemap</a></li>
<li><a href="olemeta.html">olemeta</a></li>
<li><a href="oleobj.html">oleobj</a></li>
<li><a href="oletimes.html">oletimes</a></li>
<li><a href="olevba.html">olevba</a></li>
<li><a href="pyxswf.html">pyxswf</a></li>
<li><a href="rtfobj.html">rtfobj</a></li>
</ul></li>
</ul>
</body>
</html>
