import argparse
import os
import sys
from joblib import load, dump
from collections import Counter
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, AdaBoostClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import warnings
warnings.filterwarnings('ignore')

# 尝试导入 XGBoost，如果不可用则跳过
try:
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("Warning: XGBoost not available. XGB models will be skipped during training.")


root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

dir_path = os.path.dirname(os.path.abspath(__file__)) 
MODELS_PATH = os.path.join(dir_path, 'models')
RF_PATH = os.path.join(dir_path, 'models', 'RF_all_73.joblib')

if root_path:
    import ai_office.get_features as getFeatures
    import ai_office.office_tools as tools
    import ai_office.why_result as explanation
    from utils.logger_config import setup_logger

    logfile = os.path.join(root_path, 'logs', 'ai_scan.log')
    logger = setup_logger('doc_detector', logfile)


data_types = {
    # 部分1 ：vba代码特征
    # 文档信息
    # 'file_name': 'category',
    'file_size': 'float32',
    # 函数名统计
    'autoopen': 'float32',  # 区分度极强
    'createobject': 'float32',  # 较强
    'getobject': 'float32',  # 区分度极强
    'windows': 'float32',  # 正负样本的区分度有限
    'array': 'float32',  # 区分度极强
    'environ': 'float32',  # 区分度极强
    'run': 'float32',  # 区分度较强
    'click': 'float32',  # 在所有函数名中出现的频率
    'close': 'float32',  # 在所有函数名中出现的频率
    'open': 'float32',  # 在所有函数名中出现的频率
    # 保证冗余，先不合并
    'workbook_open': 'float32',  # 区分度一般
    'document_open': 'float32',  # 区分度较强
    'document_close': 'float32',  # 区分度一般
    'auto_open': 'float32',  # 区分度一般
    'shell': 'float32',  # 指在函数名出现的频率
    'create': 'float32',  # 新增 10-12
    'files': 'float32',
    'ops': 'float32',
    'lines': 'float32',
    'prints': 'float32',
    # # TODO:新增 vba 变量名相关
    # 'var_good_ratio': 'float32',  速度太慢
    'var_digit_ratio': 'float32',
    'var_case_det': 'float32',
    'var_Axx_ratio': 'float32',
    'var_bad_num': 'float32',
    'var_too_long': 'float32',
    'var_skip_vb_num': 'float32',
    'var_else': 'float32',
    # 字符串变换
    'hex': 'float32',  # 区分度极强
    'chr': 'float32',  # 区分度较强
    'chrw': 'float32',  # 区分度极强
    'chrb': 'float32',  # 区分度极强
    'strreverse': 'float32',  # 区分度极强
    'xor': 'float32',  # 极强
    'cdate': 'float32',  # 区分度较强
    'cstr': 'float32',  # 区分度较强
    # 全局统计信息
    'math_ops': 'float32',
    'func_num': 'float32',
    'type_ops': 'float32',
    'str_ops': 'float32',
    # 部分2：olevba 工具
    'AutoExec': 'float32',
    'Suspicious': 'float32',
    'Base64 String': 'float32',  # 先在key里面找，没有就在suspicious里面找Base64
    'Hex String': 'float32',  # 先在key里面找，没有就在suspicious里面找Base64

    'Environ': 'float32',
    'Create': 'float32',
    'GetObject': 'float32',
    'Binary': 'float32',
    'System': 'float32',
    'Kill': 'float32',
    'Active': 'float32',  # ActiveWorkbook.SaveA 注意到关键字可能和工具版本相关，会变化
    'WScript.Shell': 'float32',
    'Powershell': 'float32',  # 在suspicious里面 先变化为小写
    'Call': 'float32',  # 在suspicious里面 call
    'VBHide': 'float32',  # 在suspicious里面 可能执行可执行文件
    'Print': 'float32',  # 在suspicious里面 print
    'VBA Stomping': 'float32',  # 在suspicious里面
    'Shell': 'float32',  # 在suspicious里面 含有shell就算 ShellExecute
    'ExecuteExcel4Macro': 'float32',
    'XMLhttp': 'float32',  # 含有就算
    'ShowWindow': 'float32',  # 测得时候考虑小写'ShowWindow',
    'Windows': 'float32',
    'Lib': 'float32',
    'Write': 'float32',
    'Output': 'float32',
    'Callbyname': 'float32',
    'Open': 'float32',
    'Put': 'float32',
    'File': 'float32',  # 在suspicious里面 带有file的
    'XLM macro': 'float32',  #
    'Execute': 'float32',
    # 增加IOC恶意程度
    'IOC': 'float32',  # key里面找IOC 可能含有http .exe .pif .bat .cmd .vbs .jar
    'ExeOrbat': 'float32',  # .exe .pif .bat .cmd
    'DDElink': 'float32',  # dde
    # 'MayBenign': 'float32',
    # 'MayMalicious': 'float32',
}


def adjust_features(df):
    # 将字典中的值转换为相应的数据类型
    # for key, value in data_types.items():
    #     df[key] = df[key].astype(value)

    if 'file_name' in df.columns:
        df.drop(columns=['file_name'], inplace=True)

    # 特征融合 它们共同反映的是“字符串混淆或数据隐写处理”；
    # 在恶意宏中经常联用，比如构造和解读恶意载荷、对代码做编码。
    # 若单独保留各自频次，维度多但稀疏；
    # 合并后，维度少但稠密，且能反映恶意宏的特征。
    # TODO: 特征融合 需要进一步验证，目前是手动添加的 构建一个特征融合的模块
    wait_merge_list = ['chrb', 'strreverse', 'hex', 'chrw', 'cdate']
    df['new_1'] = 0.0
    for key in wait_merge_list:
        df['new_1'] += df[key] #把上面列表的这些值加起来
    df.drop(columns=wait_merge_list, inplace=True)

    # 尝试进行特征融合
    # 变量混淆特征聚合
    # 这些特征通常用于衡量 VBA 宏中变量命名的混淆程度
    # 这些变量特征，是检测 代码自动混淆生成 的重要依据之一
    # 可以尝试拆分为两类：数字混淆强度（digit, Axx）和 长度混淆强度（too_long, skip_vb），各自聚合。
    df['new_2'] = 0.0
    drop_list = []
    for key in df.columns:
        if 'var_' in key:
            df['new_2'] += df[key]
            drop_list.append(key)
    df.drop(columns=drop_list, inplace=True)
    
    #删掉类别标签
    if 'Class' in df.columns:
        df.drop(columns=['Class'], inplace=True)

    # 选择category类型的列 转换category为数值编码
    cats = df.select_dtypes(include='category').columns
    df[cats] = df[cats].apply(lambda x: x.cat.codes)

    targ = 'Class'
    if not isinstance(targ, list):#如果targe不是列表
        # 把 'Class' 包装成列表 ['Class']，然后移除
        df = df[df.columns.difference([targ])].copy()
    else:
        # 如果targe是列表，则移除class列
        df = df[df.columns.difference(targ)].copy()

    return df


class EnsembleClassifier:
    def __init__(self, model_paths=None):
        """
        初始化集成分类器

        Args:
            model_paths (list, optional): 预训练模型文件路径列表。如果为None，则创建空的分类器用于训练。
        """
        if model_paths is not None:
            self.models = [load(path) for path in model_paths]
        else:
            self.models = []

        # 默认模型配置
        self.model_configs = {
            'RF': {
                'class': RandomForestClassifier,
                'params': {
                    'n_estimators': 100,
                    'max_depth': 10,
                    'min_samples_split': 5,
                    'min_samples_leaf': 2,
                    'random_state': 42
                }
            },
            'ADA': {
                'class': AdaBoostClassifier,
                'params': {
                    'n_estimators': 100,
                    'learning_rate': 1.0,
                    'random_state': 42
                }
            },
            'MLP': {
                'class': MLPClassifier,
                'params': {
                    'hidden_layer_sizes': (100, 50),
                    'max_iter': 500,
                    'random_state': 42,
                    'early_stopping': True,
                    'validation_fraction': 0.1
                }
            }
        }

        # 如果 XGBoost 可用，添加 XGB 配置
        if XGBOOST_AVAILABLE:
            self.model_configs['XGB'] = {
                'class': XGBClassifier,
                'params': {
                    'n_estimators': 100,
                    'max_depth': 6,
                    'learning_rate': 0.1,
                    'random_state': 42,
                    'eval_metric': 'logloss'
                }
            }

    # 提取特征
    def parse_file(self, file, just_model=False, yara_all=False):
        #TODO: 当features为str时，表明是明确的结果，无需解析文件，提取特征
        features = getFeatures.extract_doc_features(
            file, just_model, yara_all)
        if isinstance(features, str):
            return features  # 如果是str，表明是明确的结果，无需解析文件，提取特征
        # 调整特征
        features = adjust_features(features)

        return features

    def predict(self, file, just_model=False, yara_all=False):

        #这里经过了 提取特征以及adjust_features 两个函数
        features = self.parse_file(file, just_model, yara_all)

        # 如果features是str，表明是明确的结果，无需解析文件，直接根据黑白字符串返回分数
        if isinstance(features, str):
            if 'benign' in features:
                logger.info(f'benign as {features} in {file} ')
                return 0, features
            else:
                logger.info(f'malicious as {features} in {file} ')

                # TODO: 应该对恶意特征分级
                return 8, features

        #前置过滤 分数等于除了文件大小以外的特征值求和
        sum_score = (features.sum(axis=1) - features['file_size'])
        #分数为0或者 分数减去Suspicious小于4 则返回0
        #TODO:这个suspicious是什么
        if sum_score.eq(0.0).any() or (sum_score.item()-features['Suspicious'].item()) < 4.0:
            return 0, features

        # TODO: 统计典型恶意特征组合模式，注意字段名称的大小写
        modes = [
                ['AutoExec'],
                ['AutoExec', 'click'], ['autoopen', 'click'],
                ['AutoExec', 'IOC'],
                ['AutoExec', 'Suspicious'],  # 自动执行+可疑标记
                ['AutoExec', 'Suspicious', 'click'],
                ['AutoExec', 'Suspicious', 'Hex String'],
                ['AutoExec', 'Suspicious', 'Base64 String'],
                ['AutoExec', 'Suspicious', 'chr', 'create', 'createobject'],
                ['AutoExec', 'Environ', 'Execute', 'ExecuteExcel4Macro', 'File'],
                ['AutoExec', 'Suspicious', 'Shell', 'createobject'],  # 执行外部命令+创建对象
                ['AutoExec', 'Suspicious', 'Shell', 'GetObject'],  # 执行外部命令+获取对象
                ['document_open', 'Suspicious', 'Hex String'],  # 可疑标记+编码+自动打开宏
                ['AutoExec', 'Create', 'Hex String',
                    'Environ'],  # 创建操作+编码数据+环境变量利用
                ['AutoExec', 'Base64 String', 'chr', 'math_ops'],  # 编码+字符操作+数学运算操作
                ['document_open', 'Hex String', 'math_ops',
                    'str_ops'],  # 同上，考虑不同编码和字符串操作
                ['Suspicious', 'autoopen', 'Shell'],  # 自动打开宏+Shell命令
                ['Suspicious', 'Shell', 'document_open'],
                ['AutoExec', 'Suspicious', 'ShowWindow',
                    'createobject'],  # 隐藏窗口+创建对象+可疑行为
                ['AutoExec', 'Suspicious', 'ShowWindow', 'getobject'],
                ['AutoExec', 'IOC', 'Suspicious'],  # 已知妥协指标+可疑操作
                ['AutoExec', 'Binary', 'Create', 'Shell'],  # 二进制数据+创建操作+Shell命令
                ['AutoExec', 'Binary', 'Call', 'Callbyname'],
                ['AutoExec', 'Shell', 'Environ', 'Suspicious'],
                ['Call', 'Create', 'Suspicious', 'document_open', 'getobject'],
                ['Hex String', 'auto_open', 'open'],
                ['Base64 String', 'autoopen', 'str_ops', 'type_ops'],
                ['Shell', 'AutoExec', 'math_ops'],
                ['Shell', 'AutoExec', 'str_ops'],
                ['Shell', 'AutoExec', 'type_ops'],
                ['AutoExec', 'type_ops', 'math_ops', 'chr'],
                ['AutoExec', 'Create', 'Shell', 'Suspicious'],
                ['AutoExec', 'Create', 'GetObject', 'Hex String', 'ShowWindow'],
                ['Suspicious', 'auto_open', 'open'],
                ['Base64 String', 'Hex String', 'Suspicious'],
                ['Base64 String', 'Call', 'Hex String', 'Shell', 'Suspicious'],
                ['Base64 String', 'Execute', 'Suspicious', 'XLM macro'],
                ['Suspicious', 'chr', 'new_1', 'new_2', 'str_ops', 'type_ops'],
                ['auto_open', 'chr', 'create', 'createobject',
                    'shell', 'str_ops', 'type_ops'],
                ['Call', 'ExeOrbat', 'Execute', 'IOC', 'Open', 'Shell'],
                ['Base64 String', 'Suspicious', 'chr'],
                ['VBHide', 'autoopen'],

                ['AutoExec', 'Base64 String', 'Call'],
                ['Base64 String', 'Call',  'Suspicious', 'create', 'createobject'],
                ['Shell', 'Suspicious', 'VBHide', 'document_open', 'environ'],
                ['Base64 String', 'Write', 'create',
                    'createobject', 'document_open'],
                ['Base64 String', 'Call', 'Execute', 'File',
                    'Shell', 'Suspicious', 'create', 'createobject'],
                ['Base64 String', 'Hex String', 'Suspicious', 'math_ops'],
                ['Suspicious', 'chr', 'close', 'create',
                    'document_close', 'document_open'],
                ['AutoExec', 'Suspicious', 'XLM macro'],
                ['Call', 'Shell', 'Suspicious', 'autoopen'],
                ['click', 'close', 'create', 'createobject', 'cstr', 'environ'],
                ['ShowWindow', 'Suspicious', 'autoopen'],
                ['chr', 'click', 'workbook_open'],
                ['Binary', 'Kill'], ['AutoExec', 'Kill'],
                ['array', 'autoopen'], ['array', 'document_open'],
                ['WScript.Shell', 'run'],
                ['run', 'click', 'environ'],
                ['Powershell', 'VBHide'],
                ['Powershell', 'ExeOrbat'],
                ['Powershell', 'IOC'],

            # TODO:慎重追加
            ['Active', 'Hex String', 'Suspicious', 'Windows'],
        ]

        catch_mode_nums = 0
        hit_limit = 2  # TODO: 命中底线的阈值，越大检出率越低，此时2对应的检出是 95%

        can_pass = False
        for mode in modes:
            if all(features[key].item() > 0.0 for key in mode):
                catch_mode_nums += 1
                if catch_mode_nums > hit_limit:
                    can_pass = True
                    break

        if not can_pass:
            return 3, features
        
        predictions = [model.predict(features).ravel()[0]
                       for model in self.models]

        logger.info(f'predict result: {predictions} in {file} ')

        vote_mal_num = predictions.count(1)
        # TODO: 按权重计算 投票结果
        return vote_mal_num, features

    def _validate_training_data(self, X, y):
        """
        验证训练数据的格式和完整性

        Args:
            X (pd.DataFrame): 特征数据
            y (pd.Series or np.array): 标签数据

        Returns:
            tuple: (验证后的X, 验证后的y)

        Raises:
            ValueError: 当数据格式不正确时
        """
        # 检查输入类型
        if not isinstance(X, pd.DataFrame):
            raise ValueError("特征数据 X 必须是 pandas DataFrame")

        if not isinstance(y, (pd.Series, np.ndarray, list)):
            raise ValueError("标签数据 y 必须是 pandas Series, numpy array 或 list")

        # 转换标签为 numpy array
        if isinstance(y, list):
            y = np.array(y)
        elif isinstance(y, pd.Series):
            y = y.values

        # 检查数据长度一致性
        if len(X) != len(y):
            raise ValueError(f"特征数据长度 ({len(X)}) 与标签数据长度 ({len(y)}) 不一致")

        # 检查是否有空数据
        if len(X) == 0:
            raise ValueError("训练数据不能为空")

        # 检查标签值
        unique_labels = np.unique(y)
        if len(unique_labels) < 2:
            raise ValueError(f"标签数据必须包含至少2个不同的类别，当前只有: {unique_labels}")

        # 确保标签是二分类 (0, 1)
        if not all(label in [0, 1] for label in unique_labels):
            logger.warning(f"标签值不是标准的二分类格式 (0, 1)，当前标签: {unique_labels}")
            # 尝试转换标签
            if len(unique_labels) == 2:
                label_map = {unique_labels[0]: 0, unique_labels[1]: 1}
                y = np.array([label_map[label] for label in y])
                logger.info(f"已将标签映射为: {label_map}")

        logger.info(f"训练数据验证通过: {len(X)} 个样本, {X.shape[1]} 个特征")
        logger.info(f"标签分布: {dict(zip(*np.unique(y, return_counts=True)))}")

        return X, y

    def _prepare_features_for_training(self, X):
        """
        为训练准备特征数据，应用与推理时相同的特征处理流程

        Args:
            X (pd.DataFrame): 原始特征数据（已通过 extract_doc_features 提取）

        Returns:
            pd.DataFrame: 处理后的特征数据
        """
        # 应用与推理时相同的特征调整（与 parse_file 方法中的处理一致）
        X_processed = adjust_features(X.copy())

        logger.info(f"特征处理完成: {X_processed.shape[1]} 个特征")
        logger.info(f"特征列名: {list(X_processed.columns)}")

        return X_processed

    def parse_files_for_training(self, file_paths, labels):
        """
        批量处理文件进行特征提取，与 parse_file 方法保持一致的处理流程

        Args:
            file_paths (list): 文件路径列表
            labels (list): 对应的标签列表

        Returns:
            tuple: (特征DataFrame, 标签数组)
        """
        logger.info(f"开始批量提取 {len(file_paths)} 个文件的特征...")

        features_list = []
        valid_labels = []

        for i, (file_path, label) in enumerate(zip(file_paths, labels)):
            try:
                logger.info(f"处理文件 {i+1}/{len(file_paths)}: {file_path}")

                # 使用与推理时相同的特征提取流程，但启用训练模式
                features = self.parse_file_for_training(file_path)

                # 跳过字符串结果（预过滤的结果）
                if isinstance(features, str):
                    logger.warning(f"文件 {file_path} 被预过滤，跳过: {features}")
                    continue

                features_list.append(features)
                valid_labels.append(label)

            except Exception as e:
                logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
                continue

        if not features_list:
            raise ValueError("没有成功提取到任何特征数据")

        # 合并所有特征
        X = pd.concat(features_list, ignore_index=True)
        y = np.array(valid_labels)

        logger.info(f"特征提取完成: {len(X)} 个有效样本")
        return X, y

    def parse_file_for_training(self, file):
        """
        为训练模式提取单个文件的特征，与 parse_file 方法保持一致但启用训练模式

        Args:
            file (str): 文件路径

        Returns:
            pd.DataFrame or str: 处理后的特征数据或预过滤结果
        """
        # 使用训练模式提取特征，避免早期退出
        features = getFeatures.extract_doc_features(file, train_mode=True, yara_all=False)

        if isinstance(features, str):
            return features  # 如果是str，表明是明确的结果

        # 应用与推理时相同的特征调整
        features = adjust_features(features)

        return features

    def fit(self, X, y, model_types=None, test_size=0.2, random_state=42,
            custom_params=None, save_models=True, model_name_prefix="trained"):
        """
        训练集成分类器

        Args:
            X (pd.DataFrame): 特征数据（已通过 extract_doc_features 提取的原始特征）
            y (pd.Series/np.array/list): 标签数据 (0: 良性, 1: 恶意)
            model_types (list, optional): 要训练的模型类型列表，如 ['RF', 'ADA', 'MLP', 'XGB']
                                        如果为None，则训练所有可用的模型类型
            test_size (float): 测试集比例，默认0.2
            random_state (int): 随机种子，默认42
            custom_params (dict, optional): 自定义模型参数，格式为 {'RF': {...}, 'ADA': {...}}
            save_models (bool): 是否保存训练好的模型，默认True
            model_name_prefix (str): 保存模型时的文件名前缀，默认"trained"

        Returns:
            dict: 训练结果，包含每个模型的性能指标
        """
        logger.info("开始训练集成分类器...")

        # 验证训练数据
        X, y = self._validate_training_data(X, y)

        # 准备特征数据（应用与推理时相同的特征处理流程）
        X_processed = self._prepare_features_for_training(X)

        # 确定要训练的模型类型
        if model_types is None:
            model_types = list(self.model_configs.keys())
        else:
            # 验证模型类型
            invalid_types = [t for t in model_types if t not in self.model_configs]
            if invalid_types:
                raise ValueError(f"不支持的模型类型: {invalid_types}. 支持的类型: {list(self.model_configs.keys())}")

        logger.info(f"将训练以下模型类型: {model_types}")

        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X_processed, y, test_size=test_size, random_state=random_state, stratify=y
        )

        logger.info(f"数据分割完成: 训练集 {len(X_train)} 样本, 测试集 {len(X_test)} 样本")

        # 训练结果存储
        training_results = {}
        trained_models = []

        # 训练每个模型
        for model_type in model_types:
            logger.info(f"开始训练 {model_type} 模型...")

            try:
                # 获取模型配置
                model_config = self.model_configs[model_type].copy()
                model_class = model_config['class']
                model_params = model_config['params'].copy()

                # 应用自定义参数
                if custom_params and model_type in custom_params:
                    model_params.update(custom_params[model_type])
                    logger.info(f"{model_type} 使用自定义参数: {custom_params[model_type]}")

                # 创建并训练模型
                model = model_class(**model_params)
                model.fit(X_train, y_train)

                # 预测测试集
                y_pred = model.predict(X_test)
                y_pred_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None

                # 计算性能指标
                accuracy = accuracy_score(y_test, y_pred)
                report = classification_report(y_test, y_pred, output_dict=True)
                conf_matrix = confusion_matrix(y_test, y_pred)

                # 存储结果
                training_results[model_type] = {
                    'accuracy': accuracy,
                    'classification_report': report,
                    'confusion_matrix': conf_matrix.tolist(),
                    'model_params': model_params
                }

                trained_models.append(model)

                logger.info(f"{model_type} 训练完成 - 准确率: {accuracy:.4f}")

                # 保存模型
                if save_models:
                    model_filename = f"{model_type}_{model_name_prefix}_{int(accuracy*100)}.joblib"
                    model_path = os.path.join(MODELS_PATH, model_filename)
                    dump(model, model_path)
                    logger.info(f"{model_type} 模型已保存到: {model_path}")

            except Exception as e:
                logger.error(f"训练 {model_type} 模型时出错: {str(e)}")
                training_results[model_type] = {'error': str(e)}

        # 更新类的模型列表
        if trained_models:
            self.models = trained_models
            logger.info(f"集成分类器训练完成，共训练了 {len(trained_models)} 个模型")

        return training_results

    def train_from_files(self, file_paths, labels, **kwargs):
        """
        从文件路径列表训练模型（推荐使用的训练方法）

        Args:
            file_paths (list): 文件路径列表
            labels (list): 对应的标签列表 (0: 良性, 1: 恶意)
            **kwargs: 传递给 fit 方法的其他参数

        Returns:
            dict: 训练结果
        """
        # 使用新的批量特征提取方法
        X, y = self.parse_files_for_training(file_paths, labels)

        # 调用标准训练方法
        return self.fit(X, y, **kwargs)

    def save_models(self, model_name_prefix="manual_save", save_path=None):
        """
        手动保存当前训练好的模型

        Args:
            model_name_prefix (str): 模型文件名前缀
            save_path (str, optional): 保存路径，默认使用 MODELS_PATH

        Returns:
            list: 保存的模型文件路径列表
        """
        if not self.models:
            raise ValueError("没有训练好的模型可以保存")

        save_dir = save_path or MODELS_PATH
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        saved_paths = []

        for i, model in enumerate(self.models):
            # 尝试识别模型类型
            model_type = type(model).__name__.replace('Classifier', '').upper()
            if model_type == 'RANDOMFOREST':
                model_type = 'RF'
            elif model_type == 'ADABOOST':
                model_type = 'ADA'
            elif model_type == 'MULTILAYERPERCEPTRON':
                model_type = 'MLP'
            elif model_type == 'XGBCLASSIFIER':
                model_type = 'XGB'

            filename = f"{model_type}_{model_name_prefix}_{i}.joblib"
            filepath = os.path.join(save_dir, filename)

            dump(model, filepath)
            saved_paths.append(filepath)
            logger.info(f"模型已保存: {filepath}")

        return saved_paths

    def load_models(self, model_paths):
        """
        加载预训练模型

        Args:
            model_paths (list): 模型文件路径列表
        """
        self.models = []
        for path in model_paths:
            try:
                model = load(path)
                self.models.append(model)
                logger.info(f"成功加载模型: {path}")
            except Exception as e:
                logger.error(f"加载模型 {path} 失败: {str(e)}")

        logger.info(f"共加载了 {len(self.models)} 个模型")

    def get_model_info(self):
        """
        获取当前模型的信息

        Returns:
            dict: 模型信息
        """
        if not self.models:
            return {"message": "没有加载任何模型"}

        model_info = {}
        for i, model in enumerate(self.models):
            model_type = type(model).__name__
            model_info[f"model_{i}"] = {
                "type": model_type,
                "parameters": getattr(model, 'get_params', lambda: {})()
            }

        return model_info

    def train_from_files(self, file_paths, labels, **kwargs):
        """
        从文件路径列表训练模型

        Args:
            file_paths (list): 文件路径列表
            labels (list): 对应的标签列表 (0: 良性, 1: 恶意)
            **kwargs: 传递给 fit 方法的其他参数

        Returns:
            dict: 训练结果
        """
        logger.info(f"开始从 {len(file_paths)} 个文件提取特征进行训练...")

        # 提取特征
        features_list = []
        valid_labels = []

        for i, (file_path, label) in enumerate(zip(file_paths, labels)):
            try:
                logger.info(f"处理文件 {i+1}/{len(file_paths)}: {file_path}")

                # 提取特征，使用训练模式
                features = getFeatures.extract_doc_features(file_path, train_mode=True)

                # 跳过字符串结果（预过滤的结果）
                if isinstance(features, str):
                    logger.warning(f"文件 {file_path} 被预过滤，跳过: {features}")
                    continue

                features_list.append(features)
                valid_labels.append(label)

            except Exception as e:
                logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
                continue

        if not features_list:
            raise ValueError("没有成功提取到任何特征数据")

        # 合并所有特征
        X = pd.concat(features_list, ignore_index=True)
        y = np.array(valid_labels)

        logger.info(f"特征提取完成: {len(X)} 个有效样本")

        # 调用标准训练方法
        return self.fit(X, y, **kwargs)

    def save_models(self, model_name_prefix="manual_save", save_path=None):
        """
        手动保存当前训练好的模型

        Args:
            model_name_prefix (str): 模型文件名前缀
            save_path (str, optional): 保存路径，默认使用 MODELS_PATH
        """
        if not self.models:
            raise ValueError("没有训练好的模型可以保存")

        save_dir = save_path or MODELS_PATH
        saved_paths = []

        for i, model in enumerate(self.models):
            # 尝试识别模型类型
            model_type = type(model).__name__.replace('Classifier', '').upper()
            if model_type == 'RANDOMFOREST':
                model_type = 'RF'
            elif model_type == 'ADABOOST':
                model_type = 'ADA'
            elif model_type == 'MULTILAYERPERCEPTRON':
                model_type = 'MLP'
            elif model_type == 'XGBCLASSIFIER':
                model_type = 'XGB'

            filename = f"{model_type}_{model_name_prefix}_{i}.joblib"
            filepath = os.path.join(save_dir, filename)

            dump(model, filepath)
            saved_paths.append(filepath)
            logger.info(f"模型已保存: {filepath}")

        return saved_paths

    def load_models(self, model_paths):
        """
        加载预训练模型

        Args:
            model_paths (list): 模型文件路径列表
        """
        self.models = []
        for path in model_paths:
            try:
                model = load(path)
                self.models.append(model)
                logger.info(f"成功加载模型: {path}")
            except Exception as e:
                logger.error(f"加载模型 {path} 失败: {str(e)}")

        logger.info(f"共加载了 {len(self.models)} 个模型")

    def get_model_info(self):
        """
        获取当前模型的信息

        Returns:
            dict: 模型信息
        """
        if not self.models:
            return {"message": "没有加载任何模型"}

        model_info = {}
        for i, model in enumerate(self.models):
            model_type = type(model).__name__
            model_info[f"model_{i}"] = {
                "type": model_type,
                "parameters": getattr(model, 'get_params', lambda: {})()
            }

        return model_info


def get_ensemble_model():
    # 检测一个doc文件是否为恶意doc
    model_dir = MODELS_PATH
    model_paths = [
        'RF_all_73.joblib',  # 1 识别恶意很强 表明良性 必然良性
        'RF_all_55.joblib',
        'RF_1030_65.joblib',
        'ADA_1030_75.joblib',
        'ADA_all_73.joblib',
        'MLP_all_73.joblib',
        'MLP_all_55.joblib',
        'MLP_1030_11.joblib',
        'MLP_1030_111.joblib',
    ]
    model_paths = [os.path.join(model_dir, path) for path in model_paths]
    model = EnsembleClassifier(model_paths)

    return model


def identify_doc_files(doc_dir, expected_tag):
    """
    主函数，遍历文件夹，调用预测函数
    """
    ensemble = get_ensemble_model()

    doc_names = sorted(os.listdir(doc_dir))
    predicts = []

    for doc_name in doc_names:
        file = os.path.join(doc_dir, doc_name)
        if os.path.isfile(file):  # and '.doc' in doc_name:
            print(f'{file} 开始检测...', end='')
            predict = ensemble.predict(file)
            predicts.append(predict)
            if predict == expected_tag:
                print(f'[OK]')
            else:
                print(f'[FAIL]')
            print('----------------------------------')
    return predicts


def check_docs(doc_dir, ensemble, just_model=False, yara_all=False):
    doc_names = sorted(os.listdir(doc_dir))

    for doc_name in doc_names:
        file = os.path.join(doc_dir, doc_name)
        if os.path.isfile(file):
            print(f'{file} 开始检测...', end='')
            predict, feature = ensemble.predict(file, just_model, yara_all)
            print(f'{predict}', end=" ")
            if predict < 7:
                print('PASS')
            else:
                print('Malicious')
            print('----------------------------------')


def check_doc(doc_path, model, just_model=False, yara_all=False):
    # Check the pdf files
    predict, feature = model.predict(doc_path, just_model, yara_all)
    print(f'{predict} in 10')
    if isinstance(feature, str):
        print(feature)
        return
    # 获取DataFrame的第一行数据
    first_row = feature.iloc[0]

    # 打印列名和对应的数据
    for i in range(len(first_row)):
        print(f'{feature.columns[i]:20}: {first_row.iloc[i]:7.1f}', end=" ")
        if i % 2 == 0:
            print()
    print()

def get_office_models():
    # 为服务器代码提供
    modelRF = tools.load_model(RF_PATH)
    return modelRF


def get_predict(doc_path):
    ensemble = get_ensemble_model()
    res, predict = ensemble.predict(doc_path)
    return res, predict


def test():
    model = get_ensemble_model()
    # doc_dir = 'ai_office/test/Ben'
    # check_docs(doc_dir, model)

    doc_path = r"/home/<USER>/anqing/ai_new_server/ai/ai_office/data/test/10-16-62-62-xlmulti"
    check_doc(doc_path, model)



def main():
    parser = argparse.ArgumentParser(
        description="Script to process office file and directory.")
    parser.add_argument("--dir", type=str,
                        help="office-directory to be processed")
    parser.add_argument("--file", type=str, help="office-file to be processed")

    # 添加新的参数
    parser.add_argument("--faster", action='store_true',
                        help="Process the file or directory faster.")
    parser.add_argument("--yara_all", action='store_true',
                        help="Use YARA to process all files and directories")

    args = parser.parse_args()
    # Load the model
    ensemble = get_ensemble_model()

    args.faster = True
    if args.dir:
        check_docs(args.dir, ensemble, args.faster, args.yara_all)

    if args.file:
        check_doc(args.file, ensemble, args.faster, args.yara_all)


if __name__ == "__main__":
    # main()
    test()
