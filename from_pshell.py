'''分析PowerShell代码的特征
1. 不存在代码
2. 存在恶意特征
3. 存在 100% 混淆特征
4. 提取模糊的混淆特征
'''
import base64
import binascii
from collections import defaultdict
import os
import re
import subprocess
import sys
from typing import Callable, List

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    import ai_office.office_tools as tools

def analyze_ps_code(file_path):
    ps_code, is_obfuscated, obf_tag = get_powershell_code(file_path)

    if is_obfuscated:
        return True, obf_tag
    
    if ps_code is None:
        return False, None  # 特征数据为空

    # # 2 TODO:提取混淆特征
    # if obf_tag =="more_check_ps_code":
    #     features = extract_ps_features(ps_code)
    #     return False, features

    return False, None #

def extract_ps_features(ps_code):
    features = {}
    is_malicious = False
    
    return  is_malicious, features



def check_streams(streams):
    # TODO: 长度的取值有待测试，有效条件可以根据经验改动
    valid_ss = []
    small_stream = ""
    for stream in streams:
        stream = stream.strip()
        if stream.count('ï¿½') :
            continue
        # 考虑换行符是为了过滤掉现成的VBA代码
        if len(stream) >= 300 and stream.count('\n') < 5:
            valid_ss.append(stream)
            continue

        if stream.startswith("b'") and len(stream) <= 20:
            if stream.startswith("b'"):
                stream = stream[2:]
            if stream.endswith("'"):
                stream = stream[:-1]
            small_stream += stream

    if small_stream:
        valid_ss.append(small_stream)
    return valid_ss


def get_streams(doc_path):
    command1 = ['python', '.\oleTools\oletools\olevba.py', doc_path]
    try:
        result = subprocess.run(command1, check=False,
                                capture_output=True, text=True, encoding='latin-1',errors='ignore')
        text = result.stdout
    except Exception as e:
        print(f"in get_vb_code:{str(e)}")
        return ""

    if not text:
        return ""

    start = '- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -'
    end = '-------------------------------------------------------------------------------'

    pattern = start + '(.*?)' + end
    streams = re.findall(pattern, text, re.DOTALL)

    return streams


def filter_non_ascii(string):
    filtered_string = ""
    if len(string) == 0:
        return string, 0

    for char in string:
        if ord(char) < 128:
            filtered_string += char

    len_change = (len(string) - len(filtered_string))/len(string)
    return filtered_string, len_change


def clear_stream(stream):

    if isinstance(stream, bytes):
        stream = stream.decode('latin-1')

    stream = stream.strip()
    if stream.startswith("b'"):
        stream = stream[2:]
    if stream.endswith("'"):
        stream = stream[:-1]

    stream, change_ratio = filter_non_ascii(stream)

    stream = stream.strip()

    # ==在中间出现就把==后面的字符串移到头部
    if stream.count('==') == 1:
        parts = stream.split('==', maxsplit=1)  # 将字符串在第一个"=="处分割为两部分
        stream = parts[1]+parts[0]+'=='
    return stream, change_ratio


def test_stream_len(stream):
    slen = len(stream)
    # TODO: 超长的payload暂时只去一部分
    if slen > 100000:
        return stream[:500]

    return stream


def is_ps_code(s):
    keys = ['continue;case', 'if', 'foreach',
            'var', 'try', 'break', 'catch', 'system.io']
    for key in keys:
        if key in s:
            return True
    return False


def is_valid_base64(sb: str) -> bool:
    # 检查是否为base64编码
    try:
        if isinstance(sb, str):
            # TODO:这里的开头可能就是故意有问题的代码
            sb = sb[16:32]
            sb_bytes = bytes(sb, 'ascii')
        else:
            ValueError('must be bytes str or str')

        out = base64.b64decode(sb_bytes, validate=True).decode('latin-1')
        # print(out)
        if not out.isascii():
            return False

        return True

    except base64.binascii.Error:
        return False
    except ValueError:
        return False


def safe_base64_decode(s):

    if isinstance(s, str):
        s = s.encode()

    # Calculate the missing padding
    missing_padding = len(s) % 4
    if missing_padding != 0:
        s += b'=' * (4 - missing_padding)

    # Try to decode the string
    while len(s) > 0:
        try:
            s = base64.b64decode(s).decode('latin-1')
            return s
            # s = deobfuscate_script(s)
            # if is_ps_code(s):
            #     return s
        except Exception as e:
            s = s[:-4]

    return None


def deobfuscate_script(script):
    # TODO:这里的小写有何意义
    # script = script.lower()
    # 0. 移除 " + "拼接混淆
    script = script.replace('" + "', '')

    # 1. 移除单引号和双引号的字符串分隔符。
    script = script.replace('\'', '')
    script = script.replace('\"', '')

    # 2. 移除 '+' 字符串连接符号。
    script = script.replace('+', '')

    # 3. 将分割命令分解的部分组合回来（'n'+'ew-ob'+'ject' => 'new-object'）
    script = script.replace('`', '')

    # 4. 移除合并运算符 "::"
    script = script.replace('::', '')

    # 5. 移除变量名中的赋值操作符 ":="
    script = script.replace(':=', '')

    # 6. 移除多余命令之间的分号
    script = re.sub(";+", ";", script)

    # 8. 移除不可见字符
    script = re.sub(r'[\x01-\x1F\x7F]', '', script)

    script = script.replace(';', ';\n').replace('\0', '')
    # 7. 并返回去混淆后的脚本
    # TODO:脚本中被插入大量无效串
    if script.count('-') > 30:
        script = script.replace('-', '').strip()
    times = get_max_freq(script)
    stream = script
    while times >= 5:
        split_str = find_longest_substring(stream, times)
        while stream.count(split_str) > 0:
            stream = stream.replace(split_str, '')
        times = get_max_freq(stream)

    if len(stream) < len(script):
        result = re.sub(r'\d', '', stream)
        script = result

    return script


def find_most_common_substring(s, length):
    substr_freq = defaultdict(int)
    for i in range(len(s) - length + 1):
        substr = s[i:i+length]
        substr_freq[substr] += 1
    # 找出出现频率最高的子串
    if substr_freq:
        most_common_substring = max(substr_freq, key=substr_freq.get)
    else:
        most_common_substring = ''
    return most_common_substring


def find_longest_substring(s, m):
    low = 1
    high = len(s)
    result = ''

    while low <= high:
        mid = (low + high) // 2
        substr_freq = {}

        for i in range(len(s) - mid + 1):
            substr = s[i:i+mid]
            if substr in substr_freq:
                substr_freq[substr] += 1
            else:
                substr_freq[substr] = 1

            if substr_freq[substr] == m:
                result = max(result, substr, key=len)
                break
        else:
            high = mid - 1
            continue

        low = mid + 1

    return result


def get_max_freq(s: str) -> int:
    s = s.strip()
    # 长度为1的子串 出现的频率
    res_times = s.count(find_most_common_substring(s, 1))
    rep_times = 2
    more10_times = 0
    max_t = len(s)
    for i in range(3, max_t):
        split_str = find_most_common_substring(s, i)
        now_times = s.count(split_str)
        if now_times > 10:
            more10_times += 1
            if more10_times == 2:
                return now_times
        if now_times < 2:
            break

        if now_times == res_times:
            rep_times -= 1
            if rep_times == 0:
                break
        elif now_times > res_times:
            break
        else:
            res_times = now_times
            rep_times = 2

    return res_times


def find_longest_repeated_substring(input_string, repetition_count):
    length = len(input_string)
    res_len = 0   # the length of longest repeated substring
    res_str = None   # the longest repeated substring

    # Make sure repetition_count is greater than 1
    if repetition_count <= 1:
        return res_str

    # Use the sliding window algorithm
    # starting from the longest possible substring
    for sub_len in range(length//repetition_count, 0, -1):
        for i in range(0, length - sub_len * repetition_count + 1):  # move the front pointer
            # extract the substring
            sub_str = input_string[i:i+sub_len]
            # check if it is repeated for repetition_count times
            if input_string.count(sub_str) == repetition_count:
                if sub_len > res_len:  # if it is longer than res, update res_len and res_str
                    res_len = sub_len
                    res_str = sub_str
                break  # We found the longest possible repeated substring, thus we break the loop

    return res_str


def get_once_str(s: str) -> int:
    if s is None:
        return None
    s = s.strip()

    rep = 3  # 三次 找到相同频率的不同字符串中的最长字符串
    i = 3
    old_max_str = ""
    while i < len(s):
        split_str = find_most_common_substring(s, i)
        now = s.count(split_str)

        if now < 9:
            return None

        max_len_str = find_longest_repeated_substring(s, now)

        if max_len_str is None:
            break
        if max_len_str == old_max_str:
            break
        else:
            old_max_str = max_len_str

        if len(split_str) < len(max_len_str) and split_str in max_len_str:
            i = len(split_str)-1
            if len(max_len_str) - len(split_str) > rep:
                break

        i = i + 1

    return max_len_str


def remove_shell(stream):
    # 使用rfind()函数找到最后一个空格的位置
    space_index = stream.rfind(' ')

    # 如果字符串中没有空格，rfind()函数会返回-1
    if space_index == -1:
        # 如果没有空格，则返回原字符串
        return stream

    # 反之，返回最后一个空格后的所有字符串（不包括空格）
    return stream[space_index + 1:].strip()


def has_malicious_actions(ps_code):
    mal_signs = ['wscript.sleep', '.xmlhttp', 'frombase64string', 'wscript.shell', 'ershell -w hidden'
                 'winmgmts:win32_process', 'ershell ', 'downloadf', 'net.web', 'http:', '.exe',]
    for sign in mal_signs:
        if sign in ps_code.lower():
            return True, sign

    # TODO:可以更加的严格 同时出现一组
    # 1  ershell  wscript.shell net.webclient http:// putty.exe .downloadfile(
    return False, ""


def loop_split_check(stream):
    times = get_max_freq(stream)
    ps_code = ""
    while times >= 9:
        split_str = find_longest_substring(stream, times)
        while stream.count(split_str) > 0:
            stream = stream.replace(split_str, '')
        times = get_max_freq(stream)

        if len(stream) < 100:
            ps_code += stream+'\n'
            _, is_malicous, mal_tag = check_ps_code(ps_code)
            if is_malicous:
                return True, mal_tag
            break
        if 'shell' in stream.lower():
            stream = remove_shell(stream)
        if is_valid_base64(stream):
            stream = safe_base64_decode(stream)
            if stream is not None:
                ps_code += stream + '\n'
                _, is_malicous, mal_tag = check_ps_code(ps_code)
                if is_malicous:
                    return True, mal_tag
            break

    return False, None


def has_obfuscated_actions(ps_code):
    lines = ps_code.split('\n')

    # 混淆替换 {n} 记录个数
    pattern = "\{\d+\}"
    n_brackets = 0

    # 注释个数
    pattern1 = "/\*.*?\*/"
    n_comment = 0

    # ==出现的次数
    pattern2 = "=='"
    n_base64 = 0

    # return 出现
    n_return = 0
    # $  @ 出现次数
    n_dollar = 0

    for line in lines:
        # 1
        matches = re.findall(pattern, line)
        n_brackets += len(matches)
        if n_brackets > 10:
            return True, 'many_brackets_obfuscated'

        # 2
        matches = re.findall(pattern1, line)
        n_comment += len(matches)
        if n_comment > 10:
            return True, 'many_comments_obfuscated'

        # 3
        n_base64 += line.count(pattern2)
        if n_base64 > 10:
            return True, 'many_base64_obfuscated'
        # 4
        n_return += line.count('return')
        if n_return > 300:
            return True, 'many_return_obfuscated'

        # 5
        n_dollar += line.count('$') + line.count('@')
        if n_dollar > 400:
            return True, 'many_dollar_obfuscated'

    return False, ""


def check_ps_code(ps_code):
    ps_code = deobfuscate_script(ps_code)

    mal_flag, mal_sign = has_malicious_actions(ps_code)
    if mal_flag:
        return ps_code, mal_flag, mal_sign

    obf_flag, obf_sign = has_obfuscated_actions(ps_code)
    if obf_flag:
        return ps_code, obf_flag, obf_sign

    if is_ps_code(ps_code):
        lines = ps_code.split('\n')
        for line in lines:
            mal_flag, mal_sign = loop_split_check(line)
            if mal_flag:
                return ps_code, mal_flag, mal_sign

    # TODO: 检查变量名的随机混淆
    # 字符串拼接 "" + "R" + "u" + "\x6e" 'romCharC'+'ode'
    # 乱码混淆 bo/zl}$!bC5)(5bWz93bgozo)Z0)&b) *-01%$7445*##$(5678&&,}
    # 数字串 或 数字分隔符大量重复

    return ps_code, False, 'good_ps_code'


def get_powershell_code(doc_path):
    ps_code = ""
    streams = check_streams(get_streams(doc_path))
    n_streams = len(streams)
    # print("n_streams:", n_streams)
    # print({'stream':streams})

    if n_streams == 0:
        return "", False, None

    start = 0
    if n_streams % 2 == 0:
        start = int(n_streams / 2)

    end = n_streams
    for i in range(start, end):
        stream = streams[i]
        if stream is None:
            continue
        if len(stream) > 10000:
            stream = stream[:5000]
        stream, change_ratio = clear_stream(stream)
        if change_ratio > 0.8 or len(stream) <= 100:
            # TODO: 暂时认为是非Base64编码，可以优化以下判定，封装为一个函数
            stream, is_malicous, mal_tag = check_ps_code(stream)
            ps_code += stream + '\n'
            if is_malicous:
                return ps_code, True, mal_tag
            continue

        if stream == "" or stream is None:
            continue

        # 如果是16进制串
        def is_hex(s):
            try:
                int(s, 16)
                return True
            except ValueError:
                return False
        if is_hex(stream):
            stream = binascii.unhexlify(stream).decode('utf-8')
            ps_code += stream + '\n'
            ps_code, is_malicous, mal_tag = check_ps_code(ps_code)
            if is_malicous:
                return ps_code, True, mal_tag
            continue
        # 不含有英文字符
        if not re.search('[a-zA-Z]', stream):
            ps_code += stream + '\n'
            ps_code, is_malicous, mal_tag = check_ps_code(ps_code)
            if is_malicous:
                return ps_code, True, mal_tag
            continue

        if is_ps_code(stream):
            ps_code += stream + '\n'
            ps_code, is_malicous, mal_tag = check_ps_code(ps_code)
            if is_malicous:
                return ps_code, True, mal_tag
            continue

        if 'shell' in stream.lower():
            stream = remove_shell(stream)
        if is_valid_base64(stream):
            stream = safe_base64_decode(stream)
            if stream is not None:
                ps_code += stream + '\n'
            ps_code, is_malicous, mal_tag = check_ps_code(ps_code)
            if is_malicous:
                return ps_code, True, mal_tag
            continue

        # 标记Base64混淆
        has_base64_confusion = False

        # 混淆1: 一个完整串只插入一次
        old = stream
        split_string = get_once_str(stream)
        if  not split_string :
            # 无分割字符串 且 不是有效的Base64编码
            ps_code += stream + '\n'
            ps_code, is_malicous, mal_tag = check_ps_code(ps_code)
            if is_malicous:
                return ps_code, True, mal_tag
            continue
        else:
            stream = stream.replace(split_string, '').strip()
            has_base64_confusion = True
            if len(stream) < 100:
                while stream.count(split_string) > 0:
                    stream = stream.replace(split_string, '')

                ps_code += stream + '\n'
                ps_code, is_malicous, mal_tag = check_ps_code(ps_code)
                if is_malicous:
                    return ps_code, True, mal_tag+"has_base64_confusion"
                continue

            if 'shell' in stream.lower():
                stream = remove_shell(stream)
            if is_valid_base64(stream):
                stream = safe_base64_decode(stream)
                if stream is not None:
                    ps_code += stream + '\n'
                    ps_code, is_malicous, mal_tag = check_ps_code(ps_code)
                    if is_malicous:
                        return ps_code, True, mal_tag+"has_base64_confusion"
                continue

        # 混淆2: 处理同一个串分多次混淆的情况
        stream = old
        # has_base64_confusion = False 以下的判定是解开混淆之后的判定
        times = get_max_freq(stream)
        while times >= 9:
            split_str = find_longest_substring(stream, times)
            while stream.count(split_str) > 0:
                stream = stream.replace(split_str, '')
            times = get_max_freq(stream)

            if len(stream) < 100:
                ps_code += stream + '\n'
                ps_code, is_malicous, mal_tag = check_ps_code(ps_code)
                if is_malicous:
                    return ps_code, True, mal_tag+"has_base64_confusion"
                break
            if 'shell' in stream.lower():
                stream = remove_shell(stream)
            if is_valid_base64(stream):
                stream = safe_base64_decode(stream)
                if stream is not None:
                    ps_code += stream + '\n'
                    ps_code, is_malicous, mal_tag = check_ps_code(ps_code)
                    if is_malicous:
                        return ps_code, True, mal_tag+"has_base64_confusion"
                break

            print(f'--{split_str}-->{times}')

    # TODO: 需要进一步查验
    return ps_code, False, "more_check_ps_code"

def test():
    file_path = r'data\Mal_err\Mal_err0102'
    out = analyze_ps_code(file_path)
    print(out)

if __name__ == '__main__':
    tools.calculate_run_time(test)
