from collections import Counter
from enum import Enum
import glob
import mimetypes
import os
import re
import shutil
import subprocess
import sys
import time
from typing import List, Dict
import filetype
import pandas as pd  # conda install filetype
from pathlib import Path

# 模型存取工具
from joblib import dump, load


def get_path(name):
    try:
        result = subprocess.check_output(
            ['which', name], stderr=subprocess.STDOUT)
        return result.strip().decode()  # Decode to convert bytes to string
    except subprocess.CalledProcessError as e:
        print(f"Could not find {name}: ", e.output)
        return None


# TODO:或者写死pyton的路径
py_path = get_path('python')
if py_path:
    PYTHON_PATH = py_path
else:
    PYTHON_PATH = 'python'


TEST = True

if TEST:
    dir_path = os.path.dirname(os.path.abspath(__file__))
    MODELS_PATH = os.path.join(dir_path, 'models')
    MODEL_PATH = os.path.join(MODELS_PATH, 'model.joblib')
    LOG_PATH = os.path.join(dir_path, 'log')
    OLEDUMP_PATH = os.path.join(dir_path, 'stvTools', 'oledump.py')
    OLEVBA_PATH = os.path.join(dir_path, 'oleTools', 'oletools', 'olevba.py')


def save_model(model, save_path=MODEL_PATH):
    dump(model, save_path)


def load_model(model_path=MODEL_PATH):
    return load(model_path)

# 文件清理类工具


class FileType(Enum):
    IGNORE = -1  # 文件格式错误，直接忽略类型
    ERROR = 0
    PDF = 1
    OFFICE = 2
    ZIP = 3
    OTHER = 4


def get_file_type(file_path: str) -> FileType:
    """
    Returns the type of a file based on its file path.

    Args:
        file_path (str): The file path.

    Returns:
        FileType: The type of the file.
    """
    file_type = FileType.ERROR
    kind = filetype.guess(file_path)

    if kind is None:
        # print(f'Cannot guess {file_path} type!')

        def type_by_mime(mime):
            if mime.startswith('application/pdf'):
                return FileType.PDF
            elif mime.startswith('application/vnd.openxmlformats-officedocument') or mime.startswith('application/msword') or mime.startswith('application/vnd.ms-'):
                return FileType.OFFICE
            elif mime.startswith('application/zip') or mime.startswith('application/x-7z-compressed') or mime.startswith('application/x-rar-compressed'):
                return FileType.ZIP
            else:
                return FileType.OTHER

        file_name = os.path.basename(file_path)
        mime, encoding = mimetypes.guess_type(file_name)

        if mime is None:
            return file_type  # 文件结构可能已经破坏 且无后缀名

        file_type = type_by_mime(mime)
        return file_type

    if kind.mime.startswith('application/pdf'):
        file_type = FileType.PDF
    elif kind.mime.startswith('application/vnd.openxmlformats-officedocument') or kind.mime.startswith('application/msword') or kind.mime.startswith('application/vnd.ms-'):
        file_type = FileType.OFFICE
    elif kind.mime.startswith('application/zip') or kind.mime.startswith('application/x-7z-compressed') or kind.mime.startswith('application/x-rar-compressed'):
        file_type = FileType.ZIP
    else:
        file_type = FileType.OTHER

    return file_type


def rename_files(folder_path: str, file_type: FileType, tag: str) -> None:
    """
    Rename files in the specified folder path.

    Args:
        folder_path (str): The path of the folder where the files are located.
        file_type (FileType): The type of files to rename.
        malicious_tag (bool): Whether to include files with malicious tags. Defaults to False.

    Returns:
        None
    """
    files: List[str] = os.listdir(folder_path)
    goal_files: List[str] = []

    if file_type != FileType.IGNORE:
        for file in files:
            file_path = os.path.join(folder_path, file)
            if get_file_type(file_path) == file_type:
                goal_files.append(file)
    else:
        goal_files = files

    for i, file in enumerate(goal_files, start=1):
        _, file_ext = os.path.splitext(file)
        new_file_name = tag + f"{str(i).zfill(4)}{file_ext}"

        old_path = os.path.join(folder_path, file)
        new_path = os.path.join(folder_path, new_file_name)
        os.rename(old_path, new_path)


def move_files(source_folder: str, target_folder: str, prefix: str) -> None:
    """
    Move files from the source folder to the target folder if their names start with the specified prefix.

    Args:
        source_folder (str): The path to the source folder. 
        target_folder (str): The path to the target folder. 
        prefix (str): The prefix that the file names should start with. 

    Returns:
        None
    """
    # Check if the target folder exists, if not, create it
    if not os.path.exists(target_folder):
        os.makedirs(target_folder)

    # Iterate over the files in the source folder
    for filename in os.listdir(source_folder):
        # Check if the file name starts with the specified prefix
        if filename.startswith(prefix):
            # Construct the full paths for the source file and target file
            source_path = os.path.join(source_folder, filename)
            target_path = os.path.join(target_folder, filename)

            # Move the file
            shutil.move(source_path, target_path)
            print(f"Moved file: {source_path} -> {target_path}")


def copy_files_to_folder(files_path_list: List[str], dest_folder: str) -> None:
    """
    Copy files from a list of file paths to a destination folder.

    Args:
        files_path_list (List[str]): A list of file paths to copy.
        dest_folder (str): The destination folder to copy the files to.

    Returns:
        None
    """
    os.makedirs(dest_folder, exist_ok=True)

    for file_path in files_path_list:
        if os.path.isfile(file_path):
            shutil.copy(file_path, dest_folder)
        else:
            print(f"无法找到文件 {file_path}")


def count_files(folder_path: str) -> int:
    """
    Count the number of files in a folder.

    Args:
        folder_path: The path to the folder.

    Returns:
        The number of files in the folder.
    """
    files = os.listdir(folder_path)
    file_count = sum(1 for file in files if os.path.isfile(
        os.path.join(folder_path, file)))

    return file_count


def process_doc_files(dir_path, process_func, start=0, num=100000):
    """
    Process a list of  files using a given processing function.

    Args:
        dir_path (str): The directory path containing the  files.
        process_func (function): The function to be applied to each  file.
        start (int, optional): The starting index of the files to be processed. Defaults to 0.
        num (int, optional): The maximum number of files to be processed. Defaults to 100000.

    Returns:
        list: A list of results obtained from applying the processing function to each file.
    """
    ans = []

    files = os.listdir(dir_path)
    files_num = len(files) - start
    if num != 100000:
        files_num = min(num, files_num)
    interval = int(files_num / 20)

    for file_nth, filename in enumerate(files[start:start+files_num], start=start):
        file_path = os.path.join(dir_path, filename)
        try:
            result = process_func(file_path)
            ans.append(result)
            now_num = file_nth - start + 1
            if now_num % interval == 0:
                print(f"{now_num * 100.0 / files_num:3.0f}% ", end="")
                sys.stdout.flush()
        except Exception as e:
            print('Error: ' + filename + str(e))
            pass

    return ans


# 抽取信息的工具类
def get_marked_lines(text: str) -> List[int]:
    """
    Get the line numbers of the lines that contain VB macros in the output of stvtools/oledump.py.

    Args:
        text (str): The input text.

    Returns:
        List[int]: The line numbers of the marked lines.
    """
    lines = text.split('\n')
    marked_line_ids = []

    for line in lines:
        line = line.replace('m', 'M')
        line = line.replace('A', '')
        match = re.match(r'^\s*(\d+):\s*M', line)
        if match:
            marked_line_ids.append(int(match.group(1)))

    return marked_line_ids


def get_vba_stomp(doc_path):
    command1 = [PYTHON_PATH, OLEDUMP_PATH, doc_path]
    try:
        result = subprocess.run(command1, check=False,
                                capture_output=True, text=True, encoding='latin-1', errors='ignore')
        text = result.stdout
    except Exception as e:
        print(f"in get_vb_code:{str(e)}")
        return None

    # TODO:短路情形
    if text and "Warning: no OLE file" in text:
        return None

    # 获取无法解压的行 E 表示解压失败
    lines = text.split('\n')
    marked_line_ids = []

    for line in lines:
        line = line.replace('A', '')
        match = re.match(r'^\s*(\d+):\s*E', line)
        if match:
            marked_line_ids.append(int(match.group(1)))

    for eid in marked_line_ids:
        command2 = [PYTHON_PATH, OLEDUMP_PATH,
                    doc_path, '-s', str(eid)]
        try:
            result = subprocess.run(
                command2, check=False, capture_output=True, text=True, encoding='latin-1', errors='ignore')
            text = result.stdout
        except Exception as e:
            print(f"in get_vb_code:{str(e)}")
            return None

    if text:
        lines = text.strip().split('\n')
        result = ''.join(line.split()[-1] for line in lines)
        result = result.replace('.', ' ')
        result = re.sub(' +', ' ', result)
        return result

    return None


def get_vb_code(doc_path: str) -> str:
    """
    Retrieves VB code from a document path.

    Args:
        doc_path (str): The path to the document.

    Returns:
        str: The VB code retrieved from the document.
    """
    command1 = [PYTHON_PATH, OLEDUMP_PATH, doc_path]
    try:
        result = subprocess.run(command1, check=False,
                                capture_output=True, text=True, encoding='latin-1', errors='ignore')
        text = result.stdout
    except Exception as e:
        print(f"in get_vb_code:{str(e)}")

    # TODO:短路情形
    if text and "Warning: no OLE file" in text:
        return "nothing"

    # print('hello')
    # print(text)
    if text:
        # print(text)
        macro_ids = get_marked_lines(text)
        # print(f'macro_ids--{macro_ids}--')

        vb_code = ""
        if not macro_ids:
            # 尝试另一种工具
            command1 = [PYTHON_PATH, OLEVBA_PATH, doc_path]
            try:
                result = subprocess.run(command1, check=False,
                                        capture_output=True, text=True, encoding='latin-1', errors='ignore')
                text = result.stdout
            except Exception as e:
                print(f"in get_vb_code:{str(e)}")
                return ""

            if not text:
                return ""

            start = '- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -'
            end = '-------------------------------------------------------------------------------'

            pattern = start + '(.*?)' + end
            streams = re.findall(pattern, text, re.DOTALL)
            vb_code = ""
            for s in streams:
                if len(s) > 100:
                    vb_code += s
            return vb_code

        for id in macro_ids:
            try:
                command2 = [PYTHON_PATH, OLEDUMP_PATH,
                            doc_path, '-s', str(id), '-v']
                result = subprocess.run(
                    command2, check=False, capture_output=True, text=True, encoding='latin-1', errors='ignore')
                vb_code += result.stdout
            except Exception as e:
                print(f"in get_vb_code:{str(e)}")

        if vb_code:
            return vb_code

    return None


def parse_vba_functions(vb_code: str) -> Dict[str, int]:
    """
    Parses VBA functions from a file and returns a dictionary of function names and their counts.

    Args:
        file_path (str): The path to the VBA file.

    Returns:
        dict: A dictionary containing the function names as keys and their counts as values.
    """
    dict_list: List[Dict[str, int]] = []
    if vb_code == "":
        return {'ops': 0, 'lines': 0, 'prints': 0}

    lines: List[str] = vb_code.split("\n")
    # 统计特殊符号
    op_list = ['&', '+', '^', '-', '*', '/']
    ops = 0
    # 统计打印语句
    print_line = 0
    for line in lines:
        # 计数特殊符号
        for op in op_list:
            ops = ops + line.count(op)

        #
        if 'Debug.Print' in line or 'MsgBox' in line or 'Month Format(':
            print_line += 1

        # Match function name
        pattern: str = r'[^a-zA-Z_]\s*([a-zA-Z_]+)\s*\('
        matches: List[str] = re.findall(pattern, line)
        my_dict: Dict[str, int] = dict(Counter(matches))
        if len(my_dict) > 0:
            dict_list.append(my_dict)

    words = dict(sum((Counter(d) for d in dict_list), Counter()))
    words['ops'] = ops
    words['lines'] = len(lines)
    words['prints'] = print_line
    return words


def parse_vba_table(doc_path: str) -> Dict[str, List[str]]:
    """
    Parses a VBA table from a given document path and returns a dictionary of
    detection types and their corresponding keywords.

    Parameters:
        doc_path (str): The path of the document to parse.

    Returns:
        Dict[str, List[str]]: A dictionary with detection types as keys and
        lists of keywords as values.
    """
    command = [
        PYTHON_PATH,
        OLEVBA_PATH,
        doc_path
    ]
    try:
        result = subprocess.run(
            command,
            check=False,
            capture_output=True,
            text=True, encoding='latin-1', errors='ignore')

        text = result.stdout
        if 'No VBA or XLM macros' in text or 'No suspicious keyword' in text or 'not exist' in text:
            return "nothing"
    except Exception as e:
        print(f"in parse_vba_table:{str(e)}")
        text = None
    if text is None:
        return {}

    table_str = text[text.find('+----------+'):]
    lines = table_str.split('\n')
    data_lines = lines[3:]

    detection_dictionary: Dict[str, List[str]] = {}

    for line in data_lines:
        splitted_line = line.split('|')

        if len(splitted_line) < 3:
            continue

        detection_type = splitted_line[1].strip()
        if detection_type == '' or detection_type == 'String':
            continue
        if detection_type == 'Base64':
            detection_type = 'Base64 String'

        keyword = splitted_line[2].strip()
        detection_dictionary.setdefault(detection_type, []).append(keyword)

    return detection_dictionary


def get_dde_link(doc_path):
    """
    Generates a DDE (Dynamic Data Exchange) link for the given document path.

    Parameters:
        doc_path (str): The path of the document for which the DDE link is generated.

    Returns:
        int: The length of the generated DDE link text.

    Raises:
        subprocess.CalledProcessError: If an error occurs while executing the subprocess.
    """
    command = [PYTHON_PATH, './stvTools/msodde.py', doc_path]
    try:
        result = subprocess.run(command, check=False,
                                capture_output=True, text=True, encoding='latin-1', errors='ignore')
        text = result.stdout
    except Exception as e:
        print(f'in get_dde_link: {str(e)}')
        return ""

    # TODO: 用于暂时记录现实中的dde样本
    # with open(os.path.join(LOG_PATH,'dde_link.txt'), 'a', encoding='utf-8') as f:
    #     f.write(text)

    return text

# 用于统计关键字出现的频度


def count_total_frequency(keys, goal_list):
    """
    Calculate the total frequency of a list of keys in a goal list.

    Parameters:
    - keys (list): A list of keys to count the frequency of in the goal list.
    - goal_list (list): A list of words to count the frequency of the keys in.

    Returns:
    - total (int): The sum of the frequency of all keys in the goal list.
    """
    goal_list = [i.lower() for i in goal_list]
    goal_counter = Counter(goal_list)
    total = sum(goal_counter[key.lower()] for key in keys)
    return total


def contain_key_frequency(key, goal_list):
    """
    Calculates the frequency of a given key in a list of strings.

    Parameters:
        key (str): The key to search for in the list of strings.
        goal_list (list): The list of strings to search for the key.

    Returns:
        int: The total number of occurrences of the key in the list of strings.
    """
    key = key.lower()
    goal_list = [word.lower() for word in goal_list]
    total = sum(key in word for word in goal_list)
    return total

# 合并提取特征的工具


def merge_parquet_files(dir_path: str, output_path: str, tag: str) -> None:
    """
    Merge multiple Parquet files into a single file.

    Args:
        dir_path: The path to the directory containing the Parquet files.
        output_path: The path to the output Parquet file.
        tag: The tag used to filter the Parquet files.

    Returns:
        None

    Notes:
        - The function concatenates all Parquet files in the specified directory that have the specified tag in their file name.
        - The merged Parquet file is saved to the specified output path.
        - The 'Class' column in the merged Parquet file is updated based on the value of the 'tag' parameter.
    """
    parquet_files_list = [os.path.join(dir_path, file_) for file_ in os.listdir(
        dir_path) if os.path.splitext(file_)[1] == '.parquet' and tag in os.path.splitext(file_)[0]]

    merged_df = pd.concat([pd.read_parquet(file)
                          for file in parquet_files_list])
    merged_df['Class'] = 'Benign' if 'Ben' in tag else 'Malicious'  # 用于训练模型
    merged_df.to_parquet(output_path, index=False)

    # 测试
    # df = pd.read_parquet(output_path)


def merge_train_parquet(in_dir: str, out_dir: str) -> None:
    """
    Merge the parquet files in the input directory and save the merged data to the output directory.

    Args:
        in_dir (str): The input directory containing the parquet files.
        out_dir (str): The output directory where the merged parquet file will be saved.

    Returns:
        None
    """
    # 检查路径是否存在，不存在则创建
    directory = Path(out_dir)
    directory.mkdir(parents=True, exist_ok=True)

    all_mal_file = f"{out_dir}/all_mal.parquet"
    distinguish_word = "Mal_all"
    merge_parquet_files(in_dir, all_mal_file, distinguish_word)

    all_ben_file = f"{out_dir}/all_ben.parquet"
    distinguish_word = "Ben_all"
    merge_parquet_files(in_dir, all_ben_file, distinguish_word)

    ben_file = f"{out_dir}/all_ben.parquet"
    mal_file = f"{out_dir}/all_mal.parquet"

    ben_data = pd.read_parquet(ben_file)
    mal_data = pd.read_parquet(mal_file)

    all_data = pd.concat([ben_data, mal_data])
    all_data.to_parquet(f"{out_dir}/all.parquet", index=False)


def check_data_frame(parquet_path: str) -> None:
    df = pd.read_parquet(parquet_path)
    print(df.info())
    print(df.head())


def calculate_run_time(func):
    start_time = time.time()
    func()  # 调用传入的函数
    end_time = time.time()
    runtime = end_time - start_time
    print("运行时间: %.2f 秒" % runtime)


def process_files(dir_path, process_func, start=0, num=100000):
    """
    可修改版
    """
    ans = []

    files = os.listdir(dir_path)
    files_num = len(files) - start
    if num != 100000:
        files_num = min(num, files_num)
    interval = int(files_num / 20)

    for file_nth, filename in enumerate(files[start:start+files_num], start=start):
        file_path = os.path.join(dir_path, filename)
        try:
            result = process_func(file_path)
            if "" == result:
                continue
            # 这里进行 具体处理
            dir_ = 'D:\office_analyze\identity_office\VBS_codes\Mal'
            name, ext = os.path.splitext(filename)  # 分离文件名和后缀名
            new_filename = name + ".bas"  # 添加你的新后缀
            code_path = os.path.join(dir_, new_filename)
            with open(code_path, 'w', encoding='utf-8') as f:
                f.write(result)

            now_num = file_nth - start + 1
            if now_num % interval == 0:
                print(f"{now_num * 100.0 / files_num:3.0f}% ", end="")
                sys.stdout.flush()
        except Exception as e:
            print('Error: ' + filename + str(e))
            pass


def test():
    file_path = '/home/<USER>/anqing/ai_new_server/ai/ai_office/data/test/Matlab'
    # ans = get_vba_stomp(file_path)
    # print(ans)

    code = get_vb_code(file_path)
    # print(code)

    # file_path = 'data_all_office\Ben_edu\\zip.zip'
    # 'office_data/Mal/Mal_0081'
    # file_type = get_file_type(file_path)
    # print("file_type: ", file_type)

    # dir_path = 'data\other\office'
    # rename_files(dir_path, FileType.OFFICE,tag='Else_')

    # 'data_all_office\Ben_edu\Ben_edu0002.xls' #data_all_office\Mal\Mal_0001
    # f_name = 'data_all_office\Mal\Mal_0125'
    # vb_code = get_vb_code(f_name)
    # print(vb_code)
    # out_dict = parse_vba_functions(f_name)
    # for k, v in out_dict.items():
    #     print(f'{k}: {v}')

    # out = parse_vba_table(f_name)
    # for k, v in out.items():
    #     print(f'{k}: {v}')

    # in_dir ='pd_data\Ben2Mal1'
    # out_dir = 'train\Ben2Mal1'
    # merge_train_parquet(in_dir, out_dir)

    # check_data_frame('train\Ben2Mal1\\all.parquet')
    # print(count_files_in_folder('office_test_data\Mal'))

    # 提取所有的vba
    # dir_ = 'office_all_data\Mal'
    # process_files(dir_, get_vb_code, 0, 100000)

    #
    # indir = 'data_frame'
    # outdir = 'data_train\office_all'
    # merge_train_parquet(indir, outdir)

    # all_mal_file = f"data/Ben_edu.parquet"
    # distinguish_word = "Ben_edu"
    # merge_parquet_files('data_frame', all_mal_file, distinguish_word)

    # output_path = r'data_train\all_2.parquet'
    # files_list = [r'data_train\all.parquet',
    #               r'data_train\Ben_edu.parquet']
    # merged_df = pd.concat([pd.read_parquet(file)
    #                       for file in files_list])
    # merged_df.to_parquet(output_path, index=False)

    # file = r'data_train\more_ben\all.parquet'
    # df = pd.read_parquet(file)
    # print(df['Class'])


if __name__ == '__main__':
    calculate_run_time(test)
