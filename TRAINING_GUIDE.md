# EnsembleClassifier 训练功能使用指南

## 概述

`EnsembleClassifier` 类现在支持训练功能，可以训练多种机器学习模型来检测恶意Office文档。

## 支持的模型类型

- **RF**: RandomForest (随机森林)
- **ADA**: AdaBoost (自适应提升)
- **MLP**: Multi-Layer Perceptron (多层感知机)
- **XGB**: XGBoost (需要安装 xgboost 库)

## 基本使用方法

### 1. 从文件路径训练

```python
from detector import EnsembleClassifier

# 准备训练数据
train_files = [
    'path/to/benign_file1.doc',
    'path/to/benign_file2.doc',
    'path/to/malicious_file1.doc',
    'path/to/malicious_file2.doc'
]
train_labels = [0, 0, 1, 1]  # 0: 良性, 1: 恶意

# 创建分类器
classifier = EnsembleClassifier()

# 训练模型
results = classifier.train_from_files(
    train_files, 
    train_labels, 
    model_types=['RF', 'ADA', 'MLP']
)

# 查看训练结果
for model_type, result in results.items():
    print(f"{model_type}: 准确率 {result['accuracy']:.4f}")
```

### 2. 测试模型性能

```python
# 准备测试数据
test_files = ['path/to/test_file1.doc', 'path/to/test_file2.doc']
test_labels = [0, 1]

# 测试模型
test_results = classifier.test(test_files, test_labels)

# 查看测试结果
for model_name, result in test_results.items():
    print(f"{model_name}: 准确率 {result['accuracy']:.4f}")
```

### 3. 使用训练好的模型进行预测

```python
# 训练完成后，可以直接使用 predict 方法
score, features = classifier.predict('path/to/unknown_file.doc')
print(f"预测分数: {score}")
```

## 快速开始示例

```python
# 运行内置的快速训练测试
from detector import quick_train_test

# 这会自动查找 data/train/ 和 data/test/ 目录中的文件进行训练和测试
quick_train_test()
```

## 数据目录结构

建议的数据目录结构：

```
data/
├── train/
│   ├── benign/          # 良性文件
│   │   ├── file1.doc
│   │   └── file2.docx
│   └── malicious/       # 恶意文件
│       ├── malware1.doc
│       └── malware2.docx
└── test/
    ├── benign/          # 测试用良性文件
    └── malicious/       # 测试用恶意文件
```

## 模型保存

训练完成后，模型会自动保存到 `models/` 目录，文件名格式为：
`{模型类型}_trained_{准确率}.joblib`

例如：`RF_trained_85.joblib`

## 注意事项

1. 训练数据需要包含足够的良性和恶意样本
2. 文件路径必须是有效的Office文档
3. 标签必须是0（良性）或1（恶意）
4. 训练过程会自动应用与推理时相同的特征处理流程
5. 如果文件被预过滤（返回字符串结果），会自动跳过

## 错误处理

- 如果文件无法处理，会自动跳过并继续处理其他文件
- 训练过程中的错误会被捕获并记录
- 建议在训练前检查文件路径的有效性

## 性能优化建议

1. 使用足够大的训练集（建议每类至少100个样本）
2. 确保训练数据的平衡性（良性和恶意样本数量相近）
3. 根据需要选择合适的模型类型
4. 可以通过调整模型参数来优化性能
