'''分析Yara规则的结果
1，存在恶意特征
2，存在混淆特征
3，没有100%混淆特征，提取特征并返回 


'''
# python pip yara-python
import sys
import os
import subprocess

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    import ai_office.office_tools as tools

def analyze_yara_result(file_path,open_all_scan = False):
    is_malicious = False
    is_malicious, yara_res = detect_malicious_yara(file_path,open_all_scan)

    if yara_res is None:
        return is_malicious, None  # 特征数据为空
    else:
        return is_malicious, yara_res

    # is_malicious = detect_malicious_signs(yara_res)
    # if is_malicious :
    #     return is_malicious, None


def detect_malicious_yara(file_path, is_all=False):

    #  1 是否包含PE嵌入文件
    pe_text = check_pe_yara(file_path)
    if pe_text and 'yara' in pe_text:
        return True, "contains_pe_file"
    
     # 10 base64 Powershell directives
    base64_text = run_yara(file_path,'yara_rules\Base64_Encoded_Powershell_Directives.rule')
    if base64_text and 'yara' in base64_text:
        return True, "contains_base64_powershell_directives"

    # 14 office AV 静态扫描银行木马
    office_av_text = run_yara(file_path,'yara_rules\ClamAV_Emotet_String_Aggregrate.rule')
    if office_av_text and 'yara' in office_av_text:
        return True, "contains_office_Emotet_String"

    # TODO: 为了提速默认只是扫描以上三个常见检测
    if is_all:
        # 2 Microsoft Excel Data Connection
        excel_text = run_yara(file_path,'yara_rules\Microsoft_Excel_Data_Connection.rule')
        if excel_text and 'yara' in excel_text:
            return True, "contains_excel_conn"
        
        # 3 Office DDE cmd
        dde_text = run_yara(file_path,'yara_rules\Microsoft_Office_DDE_Command_Execution.rule')
        if dde_text and 'yara' in dde_text:
            return True, "contains_office_dde_cmd"
        
        # 4 embedded Flash file
        flash_text = run_yara(file_path,'yara_rules\Microsoft_Office_Document_with_Embedded_Flash_File.rule')
        if flash_text and 'yara' in flash_text:
            return True, "contains_embedded_flash"

        # 5 xlsx with macrosheet
        xlsx_text = run_yara(file_path,'yara_rules\Microsoft_XLSX_with_Macrosheet.rule')
        if xlsx_text and 'yara' in xlsx_text:
            return True, "contains_xlsx_macrosheet"
        
        # 6 MSIExec_Pivot
        msi_text = run_yara(file_path,'yara_rules\MSIExec_Pivot.rule')
        if msi_text and 'yara' in msi_text:
            return True, "contains_msiexec_pivot"
        
        # 7 IQY 
        iqy = []
        iqy_text = run_yara(file_path,'yara_rules\IQY_File.rule')
        iqy.append(iqy_text)
        iqy_text = run_yara(file_path,'yara_rules\IQY_File_With_Pivot_Extension_URL.rule')
        iqy.append(iqy_text)
        iqy_text = run_yara(file_path,'yara_rules\IQY_File_With_Suspicious_URL.rule')
        iqy.append(iqy_text)
        for iqy_text in iqy:
            if iqy_text and 'yara' in iqy_text:
                return True, "contains_iqy"
        # 8 Hex_encoded_powershell
        hex_text = run_yara(file_path,'yara_rules\Hex_Encoded_Powershell.rule')
        if hex_text and 'yara' in hex_text:
            return True, "contains_hex_encoded_powershell"
        
        # 9 CVE-2018-4878
        cve_text = run_yara(file_path,'yara_rules\CVE_2018_4878_0day_ITW.rule')
        if cve_text and 'yara' in cve_text:
            return True, "contains_cve_2018_4878"
        
    
        
        # 11 excel hidden macro sheet
        excel_macro_text = run_yara(file_path,'yara_rules\Excel_Hidden_Macro_Sheet.rule')
        if excel_macro_text and 'yara' in excel_macro_text:
            return True, "contains_excel_hidden_macro_sheet"
        
        # 12 exe convert to msi
        exe_to_msi_text = run_yara(file_path,'yara_rules\Executable_Converted_to_MSI.rule')
        if exe_to_msi_text and 'yara' in exe_to_msi_text:
            return True, "contains_exe_to_msi"
        
        # 13 木马 AgentTesla
        agent_text = run_yara(file_path,'yara_rules\AgentTesla.rule')
        if agent_text and 'yara' in agent_text:
            return True, "contains_agent_tesla"
        

        
        # 15 Hidden Bee Elements
        bee_text = run_yara(file_path,'yara_rules\Hidden_Bee_Elements.rule')
        if bee_text and 'yara' in bee_text:
            return True, "contains_hidden_bee_elements"
        
        # 16 rtf byte Nibble obfuscation
        rtf_text = run_yara(file_path,'yara_rules\RTF_Byte_Nibble_Obfuscation.rule')
        if rtf_text and 'yara' in rtf_text:
            return True, "contains_rtf_byte_nibble_obfuscation"

    return False, None


def run_yara(file_path,rule_path):
    command = ['python', '.\stvTools\oledump.py', '-y',
               # 速度更快
               rule_path,  
               #    './stvTools/contains_pe_file.yara',
               #关闭解码器加速
               #    '-D', 
               #    './stvTools/decoder_xor1',
               #    ',decoder_rol1',
               #    ',decoder_add1',
               file_path]
    try:
        result = subprocess.run(command, check=False,
                                capture_output=True, text=True, encoding='latin-1', errors='ignore')
        text = result.stdout

    except Exception as e:
        print(f'in run_pe_yara: {str(e)}')
        return  None

    text = text.lower().split('\n')
    if text is None:
        return None
    yara_result = ""
    for line in text:
        if 'yara' in line:
            yara_result += line + '\n'

    # # TODO: 用于暂时记录
    # if yara_result:
    #     with open('./log/yara_2.txt', 'a', encoding='utf-8') as f:
    #         f.write(f'\n----------{file_path}----------\n')
    #         f.write(rule_path+'\n')
    #         f.write(yara_result)

    return yara_result


def check_pe_yara(doc_path):
    command = ['python', '.\stvTools\oledump.py', '-y',
               # 速度更快
               'yara_rules\Embedded_PE.rule',  
               #    './stvTools/contains_pe_file.yara',
               #关闭解码器加速
               #    '-D', 
               #    './stvTools/decoder_xor1',
               #    ',decoder_rol1',
               #    ',decoder_add1',
               doc_path]
    try:
        result = subprocess.run(command, check=False,
                                capture_output=True, text=True, encoding='latin-1', errors='ignore')
        text = result.stdout

    except Exception as e:
        print(f'in run_pe_yara: {str(e)}')
        return None

    text = text.lower().split('\n')
    if text is None:
        return None
    yara_result = ""
    for line in text:
        if 'yara' in line:
            yara_result += line + '\n'

    # TODO: 用于暂时记录
    # if yara_result:
    #     with open('./log/yara_2.txt', 'a', encoding='utf-8') as f:
    #         f.write(f'\n----------{doc_path}----------\n')
    #         f.write('yara_rules\Embedded_PE.rule\n')
    #         f.write(yara_result)
    return yara_result


def process_files(dir_path, process_func, start=0, num=100000):
    """
    可修改版
    """
    ans = []

    files = os.listdir(dir_path)
    files_num = len(files) - start
    if num != 100000:
        files_num = min(num, files_num)
    interval = int(files_num / 20)

    for file_nth, filename in enumerate(files[start:start+files_num], start=start):
        file_path = os.path.join(dir_path, filename)
        try:
            # 这里进行 具体处理
            process_func(file_path)

            now_num = file_nth - start + 1
            if now_num % interval == 0:
                print(f"{now_num * 100.0 / files_num:3.0f}% ", end="")
                sys.stdout.flush()
        except Exception as e:
            print('Error: ' + filename + str(e))
            pass


def test():
    process_files('office_3k_data\Mal',analyze_yara_result)
    # file_path = 'office_3k_data\Mal\Mal_0017'
    # print(analyze_yara_result(file_path))


if __name__ == '__main__':
    tools.calculate_run_time(test)
